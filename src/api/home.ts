import request from '@/utils/request'

const HomeApi = {
  getExpresses: 'ejf/getExpresses', // 查询30天票件数量
  getBalance: 'bill/getBalance', // GET 查询账单余额
  getHomeNotice: 'notice/getHomeNotice', // GET 首页查询前三条公告
  accountLogin: 'account/accountLogin',
  phoneLogin: 'account/phoneLogin',
  getCheckPointBatch: 'track/getCheckPointBatch', // POST 轨迹查询-批量
}

export async function getCheckPointBatch(data: Array<string>) {
  return request<Array<EJF.getCheckPointBatchItem>>({
    loading: '批量查询轨迹',
    methodName: HomeApi.getCheckPointBatch,
    method: 'POST',
    data,
  })
}

export async function getHomeNotice() {
  return request<
    {
      contentId: number
      title: string
      content: string
      updatedate: string
      inputdate: string
      saw: number
    }[]
  >({
    // loading: '首页查询前三条公告',
    methodName: HomeApi.getHomeNotice,
    method: 'GET',
  })
}

export async function getExpresses() {
  return request<{ status: string; description: string; quantity: number }[]>({
    // loading: '查询30天票件数量',
    methodName: HomeApi.getExpresses,
    method: 'GET',
  })
}

export async function getBalance() {
  return request<{
    unSettledBalance: ''
    settledBalance: ''
    changedBalance: ''
    frozenBalance: ''
    frozenCustomer: false
    merchantCode: '100000'
    currencyName: 'USD'
  }>({
    // loading: '查询账单余额',
    methodName: HomeApi.getBalance,
    method: 'GET',
  })
}
