import request from '@/utils/request'

const RegisterApi = {
  getCheckCode: 'account/getCheckCode',
  registerUser: 'account/registerUser',
  createFaceAuth: 'merchant/apply/createFaceAuth',
  checkFaceAuth: 'merchant/apply/checkFaceAuth',
  checkSalePhone: 'merchant/apply/validateSalePhone',
  createPersonalBankFlow: 'merchant/apply/createPersonalBankFlow',
  verifyPersonalBankMobileCode: 'merchant/apply/verifyPersonalBankMobileCode',
  savePersonOne: 'merchant/apply/savePersonalApplyBankAndOfficeInfo',
  savePersonTwo: 'merchant/apply/savePersonalPrincipalContactAndEmergencyContact',
  saveCompanyOne: 'merchant/apply/saveCompanyMerchantInfoAndOfficeAddress',
  saveCompanyTwo: 'merchant/apply/saveCompanyMerchantContact',
  getAuthInfo: 'merchant/apply/getAuthInfo',
  getCompanyBankInfo: 'merchant/apply/getCompanyBankInfo',
  getBranchBank: 'merchant/apply/getBranchBankList',
  submitCompanyBankInfo: 'merchant/apply/submitCompanyBankInfo',
  validateAmount: 'merchant/apply/validateAmount',
  getCompanyName: 'merchant/apply/getCompanyName',

}

// 注册时候获取手机验证码
export async function getCheckCode(phone: string) {
  return request({
    methodName: `${RegisterApi.getCheckCode}?phone=${phone}&type=2`,
    method: 'GET',
  })
}

// 发起人脸识别和人脸识别回调
export async function createFaceAuth(params?: any, token?: any) {
  return request({
    methodName: token
      ? `${RegisterApi.checkFaceAuth}?faceToken=${token}`
      : RegisterApi.createFaceAuth,
    method: 'POST',
    data: params,
  })
}

// 校验销售手机号
export async function checkSalePhone(params?: number) {
  return request({
    methodName: `${RegisterApi.checkSalePhone}?phone=${params}`,
    method: 'GET',
  })
}

// 校验个人银行卡
export async function createPersonalBankFlow() {
  return request({
    methodName: RegisterApi.createPersonalBankFlow,
    method: 'GET',
  })
}

// 保存个人商户银行卡和办公地址信息
export async function savePersonOne(params?: any) {
  return request({
    methodName: RegisterApi.savePersonOne,
    method: 'POST',
    data: params,
  })
}

// 银行卡验证码校验
export async function verifyPersonalBankMobileCode(params: any) {
  return request({
    methodName: RegisterApi.verifyPersonalBankMobileCode,
    method: 'POST',
    data: params,
  })
}

//保存个人商户负责人信息
export async function savePersonTwo(params: any) {
  return request({
    methodName: RegisterApi.savePersonTwo,
    method: 'POST',
    data: params,
  })
}

// 保存企业信息和办公地址
export async function saveCompanyOne(params?: any) {
  return request({
    methodName: RegisterApi.saveCompanyOne,
    method: 'POST',
    data: params,
  })
}

//获取认证账号信息
export async function getAuthInfo() {
  return request({
    loading: '加载中...',
    methodName: RegisterApi.getAuthInfo,
    method: 'GET',
  })
}

// 保存企业联系人法人信息
export async function saveCompanyTwo(params: any) {
  return request({
   
    methodName: RegisterApi.saveCompanyTwo,
    method: 'POST',
    data: params,
  })
}

// 获取企业银行卡信息
export async function getCompanyBankInfo() {
  return request({
    loading: '加载中...',
    methodName: RegisterApi.getCompanyBankInfo,
    method: 'GET',
  })
}

// 根据关键字查询支行列表
export async function getBranchBank(params: string) {
  return request({
    loading: '加载中...',
    methodName: `${RegisterApi.getBranchBank}?keyword=${params}`,
    method: 'POST',
  })
}

// 提交企业银行卡信息
export async function submitCompanyBankInfo(params: any) {
  return request({
    loading: '',
    methodName: RegisterApi.submitCompanyBankInfo,
    method: 'POST',
    data: params,
  })
}

// 校验企业打款金额
export async function validateAmount(params: any) {
  return request({
    loading: '',
    methodName: `${RegisterApi.validateAmount}?amount=${params}`,
    method: 'POST',
  })
}

// 获取企业名字
export async function getCompanyName() {
  return request({
    loading: '',
    methodName: RegisterApi.getCompanyName,
    method: 'GET',
  })
}

// 用户注册
export async function registerUser(params: any) {
  return request({
    loading: '',
    methodName: RegisterApi.registerUser,
    method: 'POST',
    data: params,
  })
}



