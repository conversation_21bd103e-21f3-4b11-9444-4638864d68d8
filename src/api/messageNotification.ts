import request from '@/utils/request'

const MessageNotificationApi = {
  list: 'messageSubscription/list',
  read: 'messageSubscription/read',
}

export async function read(data: MessageNotification.ReadRequest[]) {
  return request<any, MessageNotification.ReadRequest[]>({
    methodName: MessageNotificationApi.read,
    method: 'POST',
    data,
  })
}

export async function list(data: MessageNotification.ListRequest) {
  return request<any, MessageNotification.ListRequest>({
    methodName: MessageNotificationApi.list,
    method: 'POST',
    data,
  })
}
