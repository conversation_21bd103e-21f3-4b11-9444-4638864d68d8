import request from '@/utils/request'

const BillApi = {
  getAccountPeriodList: 'bill/getAccountPeriodList', // 查询账期清单 POST
  getHistoricalBillList: 'bill/getHistoricalBillList', // 查询类型清单 POST
  getNumberList: 'bill/getNumberList', // 运单明细接口 POST
  getTypeBillDetail: 'bill/getTypeBillDetail', // 查询类型清单详情 POST
  getHistoryListByType: 'bill/getHistoryListByType', // 查询类型清单详情 POST
}

export async function getHistoryListByType(data: Bill.getHistoryListByTypeRequest) {
  return request<Bill.getHistoryListByTypeResponse, Bill.getHistoryListByTypeRequest>({
    methodName: BillApi.getHistoryListByType,
    method: 'POST',
    data,
    loading: '加载中...',
  })
}

export async function getTypeBillDetail(data: Bill.getTypeBillDetailRequest) {
  return request<Bill.getTypeBillDetailResponse, Bill.getTypeBillDetailRequest>({
    methodName: BillApi.getTypeBillDetail,
    method: 'POST',
    data,
    loading: '加载中...',
  })
}

export async function getNumberList(data: Bill.getNumberListRequest) {
  return request<Bill.getNumberListResponse, Bill.getNumberListRequest>({
    methodName: BillApi.getNumberList,
    method: 'POST',
    data,
    loading: '加载中...',

  })
}

export async function getHistoricalBillList(data: Bill.getHistoricalBillListRequest) {
  return request<Bill.getHistoricalBillListResponse, Bill.getHistoricalBillListRequest>({
    methodName: BillApi.getHistoricalBillList,
    method: 'POST',
    data,
    loading: '加载中...',

  })
}

export async function getAccountPeriodList(data: Bill.getAccountPeriodListRequest) {
  return request<Bill.getAccountPeriodListResponse, Bill.getAccountPeriodListRequest>({
    methodName: BillApi.getAccountPeriodList,
    method: 'POST',
    data,
    loading: '加载中...',

  })
}
