import request from '@/utils/request'

const EjfApi = {
  getOrder: 'ejf/getOrder', // POST 列表查询
  getOrderDetail: 'ejf/getOrderDetail', // POST 详情查询
  getCountry: 'ejf/getCountry', // POST 获取国家
  getChannel: 'ejf/getChannel', // POST 获取产品
  getCurrency: 'ejf/getCurrency', // GET 查询币种
}

export async function getCurrency() {
  return request({
    loading: '加载中...',
    methodName: EjfApi.getCurrency,
    method: 'GET',
  })
}

export async function getOrder(data: Partial<EJF.getOrderRequest>) {
  return request<EJF.getOrderResponse>({
    loading: '加载中...',
    methodName: EjfApi.getOrder,
    method: 'POST',
    data,
  })
}

export async function getOrderDetail(data: Partial<EJF.getOrderDetailRequest>) {
  return request<EJF.getOrderDetailResponse>({
    loading: '加载中...',
    methodName: EjfApi.getOrderDetail,
    method: 'POST',
    data,
  })
}

export async function getCountry(data: any) {
  return request({
    loading: '加载中...',
    methodName: EjfApi.getCountry,
    method: 'POST',
    data,
  })
}

export async function getChannel(data: any) {
  return request({
    loading: '加载中...',
    methodName: EjfApi.getChannel,
    method: 'POST',
    data,
  })
}
