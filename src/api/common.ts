import request from '@/utils/request'

const CommonApi = {
  getBankList: 'merchant/apply/getBankList',
  getPageState: 'index/getPageState',
  getMerchantApplyInfo: 'merchant/apply/getMerchantApplyInfo',
  getProvinceList: 'merchant/apply/getProvinceList', // 获取省份列表
  getCityList: 'merchant/apply/getCityList', // 获取城市列表
  getAreaList: 'merchant/apply/getAreaList', // 获取区域列表
  getMerchantInfo: 'merchant/apply/getMerchantInfo',
  unbindUser: 'account/unbindUser', // 解绑微信、
  getAccountList: 'shipper/getAccountList', //查询制单账号列表
  getPacketWarehouse: 'ejf/getWarehouses', //查询交货仓接口
}

export async function getPacketWarehouse() {
  return request({
    loading: '加载中...',
    methodName: CommonApi.getPacketWarehouse,
    method: 'GET',
  })
}

export async function getAccountList() {
  return request({
    loading: '加载中...',
    methodName: CommonApi.getAccountList,
    method: 'GET',
  })
}

export async function getBankList() {
  return request({
    loading: '正在获取银行列表',
    methodName: CommonApi.getBankList,
    method: 'GET',
  })
}

export async function getPageState() {
  return request({
    loading: '加载中...',
    methodName: CommonApi.getPageState,
    method: 'GET',
  })
}
// 获取商户申请信息
export async function getMerchantApplyInfo() {
  return request({
    loading: '加载中...',
    methodName: CommonApi.getMerchantApplyInfo,
    method: 'GET',
  })
}

export async function getProvinceList() {
  return request({
    methodName: CommonApi.getProvinceList,
    method: 'GET',
  })
}

export async function getCityList(params: string) {
  return request({
    methodName: `${CommonApi.getCityList}?provinceName=${params}`,
    method: 'GET',
  })
}

export async function getAreaList(params: string) {
  return request({
    methodName: `${CommonApi.getAreaList}?cityCode=${params}`,
    method: 'GET',
  })
}

// 获取商户信息
export async function getMerchantInfo() {
  return request({
    loading: '加载中',
    methodName: CommonApi.getMerchantInfo,
    method: 'GET',
  })
}

export async function unbindUser() {
  return request({
    loading: '退出中...',
    methodName: CommonApi.unbindUser,
    method: 'GET',
  })
}
