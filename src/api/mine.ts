import request from '@/utils/request'

const MineApi = {
  getPacketBaseInfo: 'customer/getPacketBaseInfo', // 查询业务账号冻结状态信息 GET
  getSettleCompany: 'bill/getSettleCompany', // 查询结算公司银行卡信息 GET
  getCustomerInfo: 'customer/getCustomerInfo', // 查询客户信息 GET
  getSaleAndKeFu: 'customer/getSaleAndKeFu', // 查询销售信息 GET
  getIMParam: 'customer/getIMParam',  // 获取小机器人需要的参数
  getCollectPath: 'customer/getCollectPath', // 获取通知揽收地址
}

export async function getCollectPath() {
  return request<Mine.getCollectPath>({
    // loading: '查询客户信息',
    methodName: MineApi.getCollectPath,
    method: 'GET',
  })
}

export async function getIMParam() {
  return request<Mine.getIMParam>({
    // loading: '查询客户信息',
    methodName: MineApi.getIMParam,
    method: 'GET',
  })
}

export async function getSaleAndKeFu() {
  return request<Mine.getCustomerInfoResponse>({
    // loading: '查询客户信息',
    methodName: MineApi.getSaleAndKeFu,
    method: 'GET',
  })
}

export async function getCustomerInfo() {
  return request<Mine.getCustomerInfoResponse>({
    // loading: '查询客户信息',
    methodName: MineApi.getCustomerInfo,
    method: 'GET',
  })
}

export async function getSettleCompany() {
  return request<Array<Mine.getSettleCompanyResponse>>({
    // loading: '查询结算公司银行卡信息',
    methodName: MineApi.getSettleCompany,
    method: 'GET',
  })
}

export async function getPacketBaseInfo() {
  return request<Mine.getPacketBaseInfoResponse>({
    // loading: '查询业务账号冻结状态信息',
    methodName: MineApi.getPacketBaseInfo,
    method: 'GET',
  })
}
