import request from '@/utils/request'

const MaterialApplicationApi = {
  materialOrderList: 'workOrder/materialOrderList', // 查询物料工单列表接口 POST
  materialTypeQuery: 'workOrder/materialTypeQuery', // 根据交货仓查询物料类型接口 POST
  materialCollectPoint: 'workOrder/materialCollectPoint', // 查询揽收点接口 POST
  materialCreate: 'workOrder/materialCreate', // 物料申请创建接口 POST
  materialDetail: 'workOrder/materialDetail', // 查询详情接口 POST
  materialUpdate: 'workOrder/materialOrderUpdate', // 物料申请更新接口 POST
}

export async function materialUpdate(data: MaterialApplication.MaterialUpdateRequest) {
  return request<any, MaterialApplication.MaterialUpdateRequest>({
    methodName: MaterialApplicationApi.materialUpdate,
    method: 'POST',
    data,
  })
}

export async function materialDetail(data: MaterialApplication.MaterialDetailRequest) {
  return request<any, MaterialApplication.MaterialDetailRequest>({
    methodName: MaterialApplicationApi.materialDetail,
    method: 'POST',
    data,
  })
}

export async function materialCreate(data: MaterialApplication.MaterialCreateRequest) {
  return request<any, MaterialApplication.MaterialCreateRequest>({
    methodName: MaterialApplicationApi.materialCreate,
    method: 'POST',
    data,
  })
}

export async function materialCollectPoint(data: MaterialApplication.MaterialCollectPointRequest) {
  return request<
    MaterialApplication.MaterialCollectPointResponse[],
    MaterialApplication.MaterialCollectPointRequest
  >({
    methodName: MaterialApplicationApi.materialCollectPoint,
    method: 'POST',
    data,
    isToast: false,
  })
}

export async function materialTypeQuery(data: MaterialApplication.MaterialTypeQueryRequest) {
  return request<
    Array<MaterialApplication.MaterialTypeQueryResponse>,
    MaterialApplication.MaterialTypeQueryRequest
  >({
    methodName: `${MaterialApplicationApi.materialTypeQuery}?warehouseCode=${data.warehouseCode}`,
    method: 'GET',
  })
}

export async function materialOrderList(data: MaterialApplication.materialOrderListRequest) {
  return request<
    MaterialApplication.materialOrderListResponse,
    MaterialApplication.materialOrderListRequest
  >({
    methodName: MaterialApplicationApi.materialOrderList,
    method: 'POST',
    data,
  })
}
