import request from '@/utils/request'

const FreightApi = {
  chargePrice: 'calculate/chargePrice', // POST 运价试算接口
  getCountries: 'ejf/getCountries', // GET 查询国家接口
  getWarehouses: 'plm/getWarehouses', // GET 查询交货仓接口
}

export async function getWarehouses() {
  return request({
    methodName: FreightApi.getWarehouses,
    method: 'GET',
  })
}

export async function getCountries() {
  return request({
    methodName: FreightApi.getCountries,
    method: 'GET',
  })
}

export async function chargePrice(data: Partial<Freight.chargePriceRequest>) {
  return request<Freight.chargePriceResponse>({
    methodName: FreightApi.chargePrice,
    method: 'POST',
    data,
  })
}
