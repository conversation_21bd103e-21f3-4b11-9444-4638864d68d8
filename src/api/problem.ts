import request from '@/utils/request'

const ProblemApi = {
  AllAbnormalCause: 'abnormal/AllAbnormalCause', // GET 所有仓内异常原因
  getHandleMode: 'abnormal/getHandleMode', // GET 仓内异常方案配置
  list: 'abnormal/list', // POST 仓内查询异常件列表
  orderInformation: 'abnormal/orderInformation', // POST 仓内异常件修改信息
  taxationNumber: 'abnormal/edit/taxationNumber', // POST 仓内异常件修改税号
  computational: 'abnormal/computational', // POST 仓内异常泡重计算费用
  getAddress: 'abnormal/getAddress', // POST 查询退件地址接口
  chooseHandleType: 'abnormal/chooseHandleType', // POST 异常件处理
  getStatisticsNumber: 'abnormal/getStatisticsNumber', // POST 获取异常件统计数量
}

export async function getStatisticsNumber() {
  return request<number>({
    // loading: '加载中...',
    methodName: ProblemApi.getStatisticsNumber,
    method: 'POST',
  })
}

export async function AllAbnormalCause() {
  return request<Problem.AllAbnormalCauseResponse>({
    loading: '加载中...',
    methodName: ProblemApi.AllAbnormalCause,
    method: 'GET',
  })
}

export async function getHandleMode() {
  return request({
    loading: '加载中...',
    methodName: ProblemApi.getHandleMode,
    method: 'GET',
  })
}

export async function list(data: any) {
  return request({
    loading: '加载中...',
    methodName: ProblemApi.list,
    method: 'POST',
    data,
  })
}

export async function orderInformation(data: any) {
  return request({
    loading: '加载中...',
    methodName: ProblemApi.orderInformation,
    method: 'POST',
    data,
    isToast: false,
  })
}

export async function taxationNumber(data: any) {
  return request({
    loading: '加载中...',
    methodName: ProblemApi.taxationNumber,
    method: 'POST',
    data,
  })
}

export async function computational(data: any) {
  return request({
    loading: '加载中...',
    methodName: ProblemApi.computational,
    method: 'POST',
    data,
  })
}

export async function getAddress(data: any) {
  return request({
    loading: '加载中...',
    methodName: ProblemApi.getAddress,
    method: 'POST',
    data,
  })
}

export async function chooseHandleType(data: any) {
  return request({
    loading: '加载中...',
    methodName: ProblemApi.chooseHandleType,
    method: 'POST',
    data,
  })
}
