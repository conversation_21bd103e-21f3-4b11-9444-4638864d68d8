import request from '@/utils/request'

const shipperApi = {
  getPickCityList: 'merchant/apply/getPickCityList',
  getPickCityDetail: 'merchant/apply/getPickCityById',
  getServiceStations: 'merchant/apply/getServiceStations',
  submitShipperInfo: 'merchant/apply/submitShipperInfo',
  getShipperApplyInfo: 'merchant/apply/getShipperApplyInfo',
}

// 获取取件城市列表
export async function getPickCityList() {
  return request({
    methodName: `${shipperApi.getPickCityList}`,
    method: 'GET',
  })
}

// 获取取件城市详情
export async function getPickCityDetail(param) {
  return request({
    methodName: `${shipperApi.getPickCityDetail}?id=${param}`,
    method: 'GET',
  })
}

// 获取网点列表
export async function getServiceStations() {
  return request({
    methodName: `${shipperApi.getServiceStations}`,
    method: 'GET',
  })
}

// 提交发货账号信息
export async function submitShipperInfo(params?: any) {
  return request({
    methodName: shipperApi.submitShipperInfo,
    method: 'POST',
    data: params,
  })
}

// 获取发货账号信息
export async function getShipperApplyInfo() {
  return request({
    methodName: shipperApi.getShipperApplyInfo,
    method: 'GET',
  })
}
