import request from '@/utils/request'

const LoginApi = {
  autoLogin: 'account/autoLogin',
  getCheckCode: 'account/getCheckCode',
  getYear: 'api/time/getYear',
  accountLogin: 'account/accountLogin',
  phoneLogin: 'account/phoneLogin',
  userLogout: 'account/unbindUser',
};

export async function autoLogin(code: string) {
  return request({
    loading: "正在登录",
    methodName: `${LoginApi.autoLogin}?code=${code}`,
    method: "GET",
  });
}

export async function getCheckCode(phone: string) {
  return request({
    loading: "正在获取验证码",
    methodName: `${LoginApi.getCheckCode}?phone=${phone}&type=1`,
    method: "POST",
  })
}

export async function getYear() {
  return request({
    methodName:LoginApi.getYear,
    method: "GET",
  })
}

export async function accountLogin(params) {
  return request({
    loading: "正在登录",
    methodName: LoginApi.accountLogin,
    method: "POST",
    data: params,
  })
}
export async function phoneLogin(params) {
  return request({
    loading: "正在登录",
    methodName: LoginApi.phoneLogin,
    method: "POST",
    data: params,
  })
}

export async function userLogout() {
  return request({
    methodName:LoginApi.userLogout,
    method: "GET",
  })
}
