import request from '@/utils/request'

const NoticeApi = {
  queryNoticeList: 'notice/queryNoticeList', // 查询通知公告列表 POST
  noticeRecord: 'notice/noticeRecord', // 查询通知公告详情并记录点击次数 GET
  getCustomerInfo: 'customer/getCustomerInfo', // 查询客户信息 GET
}

export async function noticeRecord(id?: string) {
  return request<Notice.noticeRecordResponse>({
    methodName: `${NoticeApi.noticeRecord}?id=${id}`,
    method: 'GET',
  })
}

export async function queryNoticeList(data: Notice.queryNoticeListRequest) {
  return request<Notice.queryNoticeListResponse>({
    // loading: '查询通知公告列表',
    methodName: NoticeApi.queryNoticeList,
    method: 'POST',
    data,
  })
}
