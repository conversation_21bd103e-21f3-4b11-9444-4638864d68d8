import { Image, Text, View } from '@tarojs/components'
import './components.scss'
import { AtNoticebar } from 'taro-ui'
import { getHomeNotice } from '@/api/home'
import { useState } from 'react'
import { useMount } from 'ahooks'
import Taro from '@tarojs/taro'
const NoticeAnnouncement = () => {
  const [content, setContent] = useState('')

  useMount(() => {
    init()
  })

  async function init() {
    try {
      const response = await getHomeNotice()
      if (response.success) {
        if (response.data.length === 0) {
          setContent('暂无通知')
        } else {
          const data = response.data.map((item, index) => `${index + 1}、${item.title}`).join(';')
          setContent(data)
        }
      }
    } catch (error) {
      console.log(error)
    }
  }

  const toNoticeList=()=>{
    Taro.navigateTo({
      url: '/pages/notifications/notificationsList/index',
    })
  }

  return (
    <View className="flex flex-col items-center">
      <View className="na_mod">
        <View className="na_cnt_row notice_custom" onClick={toNoticeList}>
          <Text className="na_instruction">通知公告</Text>
          <Image
            className="na_img"
            src="//img13.360buyimg.com/ling/jfs/t1/192742/31/44738/100/66387863Fad9478be/81ba6da29146b9d5.png"
          ></Image>
          <AtNoticebar marquee single>
            {content}
          </AtNoticebar>
          {/* <Text className="na_line1 na_txt">关于部分产品价格调整及其他相关通知</Text>
          <Text className="na_txt1">{'>'}</Text> */}
        </View>
      </View>
    </View>
  )
}

export default NoticeAnnouncement
