import { Image, Text, View } from '@tarojs/components'
import './components.scss'
import { useMount } from 'ahooks'
import { useEffect, useState } from 'react'
import { getBalance } from '@/api/home'
import Taro, { useDidShow } from '@tarojs/taro'
import { extend } from 'dayjs'
const CustomerBill = () => {
  const [billInfo, setBillInfo] = useState({
    unSettledBalance: '',
    settledBalance: '',
    changedBalance: '',
    frozenBalance: '',
    frozenCustomer: false,
  })
  const [billData,setBillData]=useState({
    unSettledBalance: '',
    settledBalance: '',
    changedBalance: '',
    frozenBalance: '',
    frozenCustomer: false,
  })
  const [eyeFlag,setEyeFlag]=useState(true)

  async function init() {
    try {
      const response = await getBalance()
      if (response.success) {
        setBillInfo({
          ...billInfo,
          ...response.data,
        })
        setBillData(response.data)
      }
    } catch (error) {
      console.log(error)
    }
  }

  useDidShow(() => {
    init()
  })

  const toBill=()=>{
    Taro.navigateTo({
      url: '/pages/bill/mineBill/index',
    })
  }


  const eyeClick=()=>{
    let flag=eyeFlag;
    setEyeFlag(!eyeFlag)
    if(flag){
      setBillInfo({
        unSettledBalance: '***',
        settledBalance: '***',
        changedBalance: '***',
        frozenBalance: '***',
        frozenCustomer: billData.frozenCustomer,
      })
    }
    if(!flag){
      setBillInfo(billData)
    }
  }

  return (
    <View className="flex flex-col items-center mb-3">
      <View className="cb_mod">
        <View className="cb_wrapper">
          <View className="cb_cnt_col">
            <Text className="cb_txt">
              账户可用余额
              <Text onClick={eyeClick} className="ml-3 at-icon at-icon-eye" />
            </Text>
            <View className="cb_price_wrap">
              <Text className="cb_yuan">
                ¥ <Text className="cb_price">{billInfo?.settledBalance}</Text>
              </Text>
              <Text onClick={toBill} className="cb_check_bill">{'查账单 >'}</Text>
            </View>
          </View>
        </View>
      </View>
      <View className="cb_content_cnt_row">
        <View className={billInfo?.frozenCustomer?'cb_content_cnt_col2':'cb_content_cnt_col3'}>
          <View className="cb_content_cnt_row2">
            <Text className="cb_content_txt3">未出账单金额</Text>
          </View>
          <View className="cb_content_cnt_row3">
            <Text className="cb_content_txt2">¥ </Text>
            <Text className="cb_content_txt1">{billInfo?.unSettledBalance}</Text>
          </View>
        </View>
        <Image
          className="cb_content_img"
          src="//img12.360buyimg.com/ling/jfs/t1/249163/33/8382/482/66387865F0efc01bd/ba576fc45c41b578.jpg"
        ></Image>
        {billInfo?.frozenCustomer && (
          <>
            <View className="cb_content_cnt_col2">
              <View className="cb_content_cnt_row2">
                <Text className="cb_content_txt3">制单预扣款金额</Text>
              </View>
              <View className="cb_content_cnt_row3">
                <Text className="cb_content_txt2">¥</Text>
                <Text className="cb_content_txt1">{billInfo?.frozenBalance}</Text>
              </View>
            </View>
            <Image
              className="cb_content_img1"
              src="//img12.360buyimg.com/ling/jfs/t1/249163/33/8382/482/66387865F0efc01bd/ba576fc45c41b578.jpg"
            ></Image>
          </>
        )}
        <View className={billInfo?.frozenCustomer?'cb_content_cnt_col2':'cb_content_cnt_col3'}>
          <View className="cb_content_cnt_row2">
            <Text className="cb_content_txt3">账户余额</Text>
          </View>
          <View className="cb_content_cnt_row3">
            <Text className="cb_content_txt2">¥ </Text>
            <Text className="cb_content_txt1">{billInfo?.changedBalance}</Text>
          </View>
        </View>
      </View>
    </View>
  )
}

export default CustomerBill
