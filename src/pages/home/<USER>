import { View } from '@tarojs/components'
import Search from './components/Search'
import SmallPacketStatus from './components/SmallPacketStatus'
import CustomerBill from './components/CustomerBill'
import NoticeAnnouncement from './components/NoticeAnnouncement'
import QuickEntrance from './components/QuickEntrance'
import { useState } from 'react'
import './index.scss'
import { useMount, useUpdateEffect } from 'ahooks'
import Taro from '@tarojs/taro'
import useGetAccountList from '@/hooks/useGetAccountList'
import { list, getStatisticsNumber } from '@/api/problem'

const Index = () => {
  const [code, setCode] = useState(undefined)

  useMount(() => {
    getProblemList()
  })

  const getProblemList = async () => {
    try {
      const response = await getStatisticsNumber()
      if (response.success) {
        if (response?.data > 0) {
          Taro.showTabBarRedDot({
            index: 2,
          })
        } else {
          Taro.hideTabBarRedDot({
            index: 2,
          })
        }
      }
    } catch (error) {
      console.log(error)
    }
  }

  // useUpdateEffect(() => {
  //   getProblemList()
  // }, [customerData])

  return (
    <View className="bg-content">
      <Search />
      <SmallPacketStatus />
      <CustomerBill />
      <NoticeAnnouncement />
      <QuickEntrance />
    </View>
  )
}

export default Index
