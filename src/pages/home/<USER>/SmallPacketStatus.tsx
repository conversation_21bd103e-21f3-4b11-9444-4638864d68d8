import { Image, Text, View } from '@tarojs/components'
import './components.scss'
import { useMount } from 'ahooks'
import { useState } from 'react'
import { getExpresses } from '@/api/home'
import Systems from '@/static/image/icon-systems.png'
import Received from '@/static/image/icon-received.png'
import Transit from '@/static/image/icon-transit.png'
import { useDidShow } from '@tarojs/taro'

const SmallPacketStatus = () => {
  const [expresses, setExpresses] = useState([
    { status: '0', title: '已制单', image: Systems, quantity: '' },
    { status: '2', title: '已收货', image: Received, quantity: '' },
    { status: '3', title: '运输途中', image: Transit, quantity: '' },
  ])

  useDidShow(() => {
    init()
  })

  async function init() {
    try {
      const response = await getExpresses()
      if (response.success) {
        const data = response.data
        const newExpresses = expresses.map(item => {
          item.quantity = data?.[item.status] ?? 0 + ''
          return item
        })
        setExpresses(newExpresses)
      }
    } catch (error) {
      console.log(error)
    }
  }

  return (
    <View className="sps_mod">
      <View className="sps_cnt_row">
        {expresses.map((item, index) => (
          <>
            <View className="sps_cnt_col2">
              <View className="sps_cnt_row2">
                <Image
                  className="index_sps_icon"
                  src={item?.image}
                />
                <Text className="sps_txt3">{item?.title}</Text>
              </View>
              <View className="sps_cnt_row3">
                <Text className="sps_txt1">{item?.quantity}</Text>
                <Text className="sps_txt2">票</Text>
              </View>
            </View>
            {index === expresses.length - 1 ? null : (
              <Image
                className="sps_img"
                src="//img12.360buyimg.com/ling/jfs/t1/249163/33/8382/482/66387865F0efc01bd/ba576fc45c41b578.jpg"
              />
            )}
          </>
        ))}
      </View>
      <View className="sps_cnt_row_text">
        <Text className="sps_cnt_row_text_content">近30天记录</Text>
      </View>
    </View>
  )
}

export default SmallPacketStatus
