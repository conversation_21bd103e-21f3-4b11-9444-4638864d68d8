import { Image, View, Input } from '@tarojs/components'
import './components.scss'
import { AtIcon } from 'taro-ui'
import Taro from "@tarojs/taro"

const Search = () => {
  const toWaybillNums=()=>{
    Taro.navigateTo({
      url: '/pages/trajectory/search/index',
    })
  }
  return (
    <View className="mod">
      <View className="cnt_row w-full ">
        <AtIcon value="search" size="7" color="#b7bac4" className="ml-[10px]" />
        <Input onClick={toWaybillNums} className="section" placeholder="输入运单号/订单号查询轨迹"/>
      </View>
    </View>
  )
}

export default Search
