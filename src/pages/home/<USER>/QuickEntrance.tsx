import { Image, Text, View } from '@tarojs/components'
import './components.scss'
import Empty from '@/components/Empty'
import Future from '@/static/image/future.png'
import Taro from '@tarojs/taro'
import { userLogout } from '@/api/login'
import { getCollectPath } from '@/api/mine'
import GJCX from '@/static/image/index_gjcx.png'
import QLSQ from '@/static/image/index_qlsq.png'
import TZLS from '@/static/image/index_tzls.png'
import WDZD from '@/static/image/index_wdzd.png'
import XXTZ from '@/static/image/index_xxtz.png'
import YJSS from '@/static/image/index_yjss.png'

const QuickEntrance = () => {
  const data = [
    {
      name: '通知揽收',
      icon: TZLS,
      onClick: () => {
        toCollect();
      },
    },
    {
      name: '运费试算',
      icon: YJSS,
      onClick: () => {
        Taro.navigateTo({
          url: '/pages/freight/search/index',
        })
      },
    },
    {
      name: '轨迹查询',
      icon: GJCX,
      onClick: () => {
        Taro.navigateTo({
          url: '/pages/trajectory/search/index',
        })
      },
    },
    {
      name: '我的账单',
      icon: WDZD,
      onClick: () => {
        Taro.navigateTo({
          url: '/pages/bill/mineBill/index',
        })
      },
    },
    {
      name: '物料申请',
      icon: QLSQ,
      onClick: () => {
        Taro.navigateTo({
          url: '/pages/materialApplication/list/index',
        })
      },
    },
    {
      name: '消息通知',
      icon: XXTZ,
      onClick: () => {
        Taro.navigateTo({
          url: '/pages/messageNotification/list/index',
        })
      },
    },
  ]

  const toCollect= async ()=>{
    try {
      const response = await getCollectPath()
      if (response.success) {
        let data1 = JSON.stringify(response.data);
        Taro.navigateTo({
          url: `/pages/noticeCollect/index?collect=${data1}`,
        })
      }
    } catch (error) {
      console.log(error)
    }
  }

  return (
    <View className="flex flex-col items-center">
      <View className="qe_mod  ">
        {/* grid grid-cols-4 gap-x-4 gap-y-5 */}
        <View className="grid grid-cols-4 gap-x-4 gap-y-5">
          {data?.map((item, index) => (
            <View className="flex flex-col items-center" onClick={item?.onClick}>
              {/* <View className="qe_img" /> */}
              <Image src={item?.icon} className="qe_img" />
              <Text className="qe_text">{item?.name}</Text>
            </View>
          ))}
        </View>
        {/* {Array.from({ length: 7 }).map((_, index) => (
          <View className="">
            <View className="flex flex-col items-center">
              <Image src={guijichaxun} className="qe_img" />
              <Text className="qe_text">轨迹查询</Text>
            </View>
          </View>
        ))} */}
        {/* <Empty
          description={
            <View className="flex flex-col items-center">
              <View className="text-[12px] text-[#999999] mb-[8px]">更多功能正在开发中</View>
              <View className="text-[18px] text-[#80C555]">敬请期待</View>
            </View>
          }
          image={<Image className="w-[180px] h-[180px]" src={Future} />}
        /> */}
      </View>
    </View>
  )
}

export default QuickEntrance
