import { useState, useEffect } from 'react'
import { Button, Text, View } from '@tarojs/components'
import { AtList, AtListItem } from 'taro-ui'
import merchantInfo from '@/types/merchantInfo'
import { getMerchantInfo } from '@/api/common'
import { autoLogin } from '@/api/login'
import usePageStatus from '@/hooks/usePageStatus'
import Taro from '@tarojs/taro'
import { getPageState, unbindUser } from '@/api/common'

const index = () => {
  const [form, setForm] = useState<merchantInfo>()
  const [pageStatus, setPageStatus] = useState<number>(0)
  const { getPageStatus } = usePageStatus()
  const [isLogin, setIsLogin] = useState<boolean>(false)

  useEffect(() => {
    Taro.clearStorageSync()
    noPwdLogin()
  }, [])

  // 自动登录
  const noPwdLogin = async () => {
    await Taro.login({
      success: async (res) => {
        try {
          const response: Response = await autoLogin(res.code)
          if (response.success) {
            setIsLogin(true)
            await Taro.showToast({
              title: '登录成功',
            })
            const result = await getPageState()
            if (result.success) {
              setPageStatus(+result.data)
              if (+result.data === 0) {
                await Taro.setNavigationBarTitle({
                  title: '商户信息',
                })
                await initalFun()
              } else {
                await Taro.setNavigationBarTitle({
                  title: '首页',
                })
                await getPageStatus()
              }
            }
          } else {
            setIsLogin(false)
            // 还未登录过
          }
        } catch (error) {
          console.log('home', error)
        }
      },
    })
  }

  const initalFun = async () => {
    try {
      const response = await getMerchantInfo()
      if (response.success) {
        setForm({
          ...form,
          ...response.data,
        })
      }
    } catch (error) {
      console.error(error)
    }
  }


  const handleOutLogin = async () => {
    try {
      const response = await unbindUser()
      if (response.success) {
        Taro.redirectTo({
          url: '/pages/login/index',
        })
      }
    } catch (e) {
      console.error(e)
    }
  }

  const changeTypeUI = () => {
    if (form?.customerType == 1) {
      return (
        <>
          <AtListItem title='法人姓名' note={form?.corporateName ?? ''} />
          <AtListItem title='法人身份证号' note={form.corporateCard ?? ''} />
        </>
      )
    } else {
      return <AtListItem title='身份证号' note={form?.personalCard ?? ''} />
    }
  }

  const changeUI = () => {
    if (!isLogin) {
      // 还未登录

      return (
        <>
          <View className='text-center mt-3'>
            您还未登录...
          </View>
          <Button className='h-10 leading-10 m-20' type='primary' onClick={() => {
            Taro.redirectTo({
              url: '/pages/login/index',
            })
          }}>立即登录</Button>
        </>
      )
    } else {
      if (pageStatus === 0) {
        return (
          <>
            <AtList>
              <AtListItem title='商户类型' note={form?.customerType == 1 ? '企业' : '个人'} />
              <AtListItem title='商户号' note={form?.code ?? ''} />
              <AtListItem title='商户名' note={form?.customerName ?? ''} />
              {changeTypeUI()}
              <AtListItem title='管理员用户名' note={form?.adminName ?? ''} />
              <AtListItem title='管理员手机号' note={form?.adminPhone ?? ''} />
              <AtListItem title='管理员邮箱' note={form?.adminEmail ?? ''} />
            </AtList>
            <Button className='h-10 leading-10 m-20' type='primary' onClick={handleOutLogin}>退出登录</Button>
          </>
        )
      } else if (pageStatus === 10) {
        return (
          <>
            <View className='text-center'>
              <View>待签约中</View>
              <View className='my-2'>
                请到PC端签约...
              </View>
            </View>
            <Button className='h-10 leading-10 m-20' type='primary' onClick={handleOutLogin}>退出登录</Button>
          </>
        )
      } else if (pageStatus === 12) {
        return (
          <>
            <View className='text-center text-sm mt-20'>
              当前商户正处于流程操作中，请去客户中心完成流程操作！
            </View>
            <Button className='h-10 leading-10 m-20' type='primary' onClick={handleOutLogin}>退出登录</Button>
          </>
        )
      }

    }
  }


  return (
    <View>
      {changeUI()}
    </View>
  )
}

export default index
