import React from 'react'
import { AtModal, AtModalContent, AtButton } from 'taro-ui'
import {View, Image} from '@tarojs/components'
import SaleserviceIcon from '@/static/image/saleservice-icon.png'
import PhoneserviceIcon from '@/static/image/phoneservice-icon.png'
import Taro from "@tarojs/taro"

type Props = {
  isOpened: boolean
  name: string
  phone: any
  keyTitle: string
  configId: string
  onClose: () => void
}

function ServiceModal(props: Props) {
  const { isOpened, keyTitle, name, phone,configId,onClose} = props

  const toPhone = (phone: string) => {
    if(!phone){
      return;
    }
    Taro.makePhoneCall({
      phoneNumber: phone, // 替换成实际的电话号码
      success: function () {

      },
      fail: function (error) {

      }
    })
  }

  const toKeFu=()=>{
    onClose();
    Taro.navigateTo({
      url: `/pages/kefu/index?configId=${configId}`,
    })
  }

  return (
      <AtModal isOpened={isOpened} onClose={onClose}>
        <View className='text-center rounded-[5px]'>
          <AtModalContent>
            <View className='at-col at-col-12 flex flex-col'>
              <View className='flex flex-col items-center mt-2'>
                <View className='text-[18px]'>{keyTitle}</View>
                <View className='bg-[#e8e8e8] rounded-full p-3 mt-3 w-7 h-7 place-items-center'>
                  <Image
                    className="customer_service_info_img"
                    src={keyTitle === "专属销售经理" ? SaleserviceIcon : PhoneserviceIcon}
                  />
                </View>
                <View className='text-[14px] text-[#333333] mt-2'>{name}</View>
              </View>
              <View className='flex at-row__justify--around mt-4'>
                <AtButton onClick={()=>toPhone(phone)} className="minebutton border-[1px] border-[#D8D8D8] bg-[#ffffff] text-[#3d3d3d]" type="secondary">
                  电话沟通
                </AtButton>
                <AtButton className="minebutton" type="primary" onClick={toKeFu}>企业微信</AtButton>
              </View>
            </View>
          </AtModalContent>
        </View>
      </AtModal>
  )
}

export default ServiceModal
