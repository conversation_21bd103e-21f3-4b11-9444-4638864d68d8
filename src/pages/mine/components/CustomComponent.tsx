import { View, Image } from '@tarojs/components'
import React, { useState } from 'react'
import Taro from '@tarojs/taro'
import Payment from '@/static/image/icon-payment.png'
import Inform from '@/static/image/icon-inform.png'
import Approvement from '@/static/image/icon-approve.png'
import IconPhone from '@/static/image/icon-phone.png'
import IconSale from '@/static/image/icon-sale.png'
import ServiceModal from '@/pages/mine/components/ServiceModal'
import { useMount } from 'ahooks'
import { getSaleAndKeFu } from '@/api/mine'

const CustomComponent = () => {
  const [modalVisible, setModalVisible] = useState(false)
  const [name, setName] = useState('')
  const [phone, setPhone] = useState('')
  const [configId, setConfigId] = useState('')
  const [keyTitle, setKeyTitle] = useState('')
  const [data, setData] = useState([
    {
      name: '我的账单',
      image: Payment,
    },
    // {
    //   name: '运费明细',
    //   image: Payment,
    // },
    { name: '通知公告', image: Inform },
    { name: '我的资料', image: Approvement },
  ])
  const [saleData, setSaleData] = useState({
    saleName: undefined,
    salePhone: '',
    keFuName: undefined,
    keFuPhone: '',
    saleConfigId: '',
    keFuConfigId: '',
  })

  useMount(() => {
    init().then((r) => {})
  })

  async function init() {
    try {
      const response = await getSaleAndKeFu()
      if (response.success) {
        setSaleData({
          ...saleData,
          ...response.data,
        })
      }
    } catch (error) {
      console.log(error)
    }
  }

  function handleRouter(params: (typeof data)[0]) {
    switch (params.name) {
      case '我的账单':
        Taro.navigateTo({
          url: '/pages/bill/mineBill/index',
        })
        break
      // case '账期清单':
      //   Taro.navigateTo({
      //     url: '/pages/bill/accountPeriodList/index',
      //   })
      //   break
      // case '运费明细':
      //   Taro.navigateTo({
      //     url: '/pages/bill/freightBillSearch/index',
      //   })
      //   break
      case '通知公告':
        Taro.navigateTo({
          url: '/pages/notifications/notificationsList/index',
        })
        break
      case '我的资料':
        Taro.navigateTo({
          url: '/pages/myProfile/index',
        })
        break
      default:
        break
    }
  }

  const modalVisibleChange = (key, name, phone, configId) => {
    setModalVisible(true)
    setKeyTitle(key)
    setName(name)
    setPhone(phone)
    setConfigId(configId)
  }
  return (
    <View className="custom_component">
      <View
        onClick={() =>
          modalVisibleChange(
            '专属销售经理',
            saleData.saleName,
            saleData.salePhone,
            saleData.saleConfigId
          )
        }
      >
        <View className="custom_component_body">
          <Image className="custom_component_body_img" src={IconSale} />
          <View className="custom_component_body_text">我的专属销售</View>
        </View>
      </View>
      {saleData.keFuName === saleData.saleName ? (
        ''
      ) : (
        <View
          onClick={() =>
            modalVisibleChange(
              '专属客服经理',
              saleData.keFuName,
              saleData.keFuPhone,
              saleData.keFuConfigId
            )
          }
        >
          <View className="custom_component_body">
            <Image className="custom_component_body_img" src={IconPhone} />
            <View className="custom_component_body_text">我的专属客服</View>
          </View>
        </View>
      )}
      {data.map((item, index) => {
        return (
          <View key={index} onClick={() => handleRouter(item)}>
            <View
              className={
                index + 1 !== data.length
                  ? 'custom_component_body'
                  : 'custom_component_body_not_border'
              }
            >
              <Image className="custom_component_body_img" src={item?.image} />
              <View className="custom_component_body_text">{item?.name}</View>
            </View>
          </View>
        )
      })}
      <ServiceModal
        keyTitle={keyTitle}
        name={name}
        phone={phone}
        configId={configId}
        isOpened={modalVisible}
        onClose={() => setModalVisible(false)}
      />
    </View>
  )
}

export default CustomComponent
