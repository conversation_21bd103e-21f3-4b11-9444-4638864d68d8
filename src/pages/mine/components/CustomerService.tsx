import { Text, View, Image } from '@tarojs/components'
import { useState } from 'react'
import { getSaleAndKeFu } from '@/api/mine'
import { useMount } from 'ahooks'
import Taro from '@tarojs/taro'
import {AtButton} from "taro-ui"
import SaleserviceIcon from '@/static/image/saleservice-icon.png'
import PhoneserviceIcon from '@/static/image/phoneservice-icon.png'

const CustomerService = () => {
  const [saleData, setSaleData] = useState({
    saleName: undefined,
    salePhone: '',
    keFuName: undefined,
    keFuPhone: '',
  })

  useMount(() => {
    init().then(r => {})
  })

  async function init() {
    try {
      const response = await getSaleAndKeFu()
      if (response.success) {
        setSaleData({
          ...saleData,
          ...response.data,
        })
      }
    } catch (error) {
      console.log(error)
    }
  }

  const toPhone = (phone: string) => {
    if(!phone){
      return;
    }
    Taro.makePhoneCall({
      phoneNumber: phone, // 替换成实际的电话号码
      success: function () {

      },
      fail: function (error) {

      }
    });
  }

  return (
    // <View className="customer_service">
    //   {/*销售*/}
    //   <View className="customer_service_info">
    //     <View>
    //       <Image
    //         className="customer_service_info_img"
    //         src={
    //           'https://storage.360buyimg.com/imgtools/e067cd5b69-07c864c0-dd02-11ed-8b2c-d7f58b17086a.png'
    //         }
    //       />
    //     </View>
    //     <View className="customer_service_info_content">
    //       <View className="customer_service_info_content_body">
    //         <View>
    //           <Text className="customer_service_info_content_text">{saleData.saleName}</Text>
    //         </View>
    //         <View className="customer_service_info_content_logo">
    //           <Text>专属销售经理</Text>
    //         </View>
    //       </View>
    //       <View className="customer_service_info_content_body customer_service_info_content_body_next">
    //         <View className="customer_service_info_content_phone mr_10">
    //           <Text>企业微信</Text>
    //           <Text className="pl-1">{'>'}</Text>
    //         </View>
    //         <View className="customer_service_info_content_phone">
    //           <Text onClick={()=>toPhone(saleData.salePhone)}>电话沟通</Text>
    //           <Text className="pl-1">{'>'}</Text>
    //         </View>
    //       </View>
    //     </View>
    //     <View className={'customer_service_info_divider'} />
    //   </View>
    //   {/*客服*/}
    //   <View className="customer_service_info">
    //     <View>
    //       <Image
    //         className="customer_service_info_img"
    //         src={
    //           'https://storage.360buyimg.com/imgtools/e067cd5b69-07c864c0-dd02-11ed-8b2c-d7f58b17086a.png'
    //         }
    //       />
    //     </View>
    //     <View className="customer_service_info_content">
    //       <View className="customer_service_info_content_body">
    //         <View>
    //           <Text className="customer_service_info_content_text">{saleData.keFuName}</Text>
    //         </View>
    //         <View className="customer_service_info_content_logo">
    //           <Text>专属客服经理</Text>
    //         </View>
    //       </View>
    //       <View className="customer_service_info_content_body customer_service_info_content_body_next">
    //         <View className="customer_service_info_content_phone mr_10">
    //           <Text>企业微信</Text>
    //           <Text className="pl-1">{'>'}</Text>
    //         </View>
    //         <View className="customer_service_info_content_phone">
    //           <Text onClick={()=>toPhone(saleData.keFuPhone)}>电话沟通</Text>
    //           <Text className="pl-1">{'>'}</Text>
    //         </View>
    //       </View>
    //     </View>
    //
    //   </View>
    // </View>
    <View className='at-row at-row__justify--around'>
      <View className='at-row m-3 mr-4 mb-0'>
      <View className= {saleData.keFuName === saleData.saleName ? 'at-col at-col-12 flex flex-col rounded-[5px] bg-[#ffffff] p-3' :'at-col at-col-6 flex flex-col rounded-[5px] bg-[#ffffff] p-3'}>
        <View className='flex justify-center mt-2'>
          <View>
            <Image
              className="customer_service_info_img"
              src={SaleserviceIcon}
            />
          </View>
          <View className='flex flex-col ml-2'>
            <View className='text-[14px] text-[#333333]'>{saleData.saleName}</View>
            <View className='wxbutton text-[10px] rounded-full border-[0px] bg-[#DAF0CD] text-[#3E9509] mt-2 p-1 pl-2 pr-2'>专属销售经理</View>
          </View>
        </View>
        <View className='flex at-row__justify--around mt-3'>
          <AtButton  onClick={()=>toPhone(saleData.salePhone)} className="minebutton border-[1px] border-[#D8D8D8] bg-[#ffffff] text-[#3d3d3d]" type="secondary">
            电话沟通
          </AtButton>
          <AtButton className="minebutton" type="primary">企业微信</AtButton>
        </View>
      </View>
      {saleData.keFuName === saleData.saleName ? '' :
      <View className='at-col at-col-6 flex flex-col rounded-[5px] bg-[#ffffff] p-3 ml-2 mr-2'>
        <View className='flex justify-center mt-2'>
          <View>
            <Image
              className="customer_service_info_img"
              src={PhoneserviceIcon}
            />
          </View>
          <View className='flex flex-col ml-2'>
            <View className='text-[14px] text-[#333333]'>{saleData.keFuName}</View>
            <View className='wxbutton text-[10px] rounded-full border-[0px] bg-[#DAF0CD] text-[#3E9509] mt-2 p-1 pl-2 pr-2'>专属客服经理</View>
          </View>
        </View>
        <View className='flex at-row__justify--around mt-3'>
          <AtButton onClick={()=>toPhone(saleData.keFuPhone)} className="minebutton border-[1px] border-[#D8D8D8] bg-[#ffffff] text-[#3d3d3d]" type="secondary">
            电话沟通
          </AtButton>
          <AtButton className="minebutton" type="primary">企业微信</AtButton>
        </View>
      </View>
        }
      </View>
    </View>
  )
}

export default CustomerService
