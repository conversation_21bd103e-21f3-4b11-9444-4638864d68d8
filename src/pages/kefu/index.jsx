import React, { Component } from 'react'
import { View } from '@tarojs/components'
import Taro from '@tarojs/taro'

export default class Index extends Component {
  // @ts-ignore
  state = {
    configId: '',
  };

  componentDidMount() {
    const { router } = Taro.getCurrentInstance();
    const { params } = router;
    this.setState({configId:params.configId})
  }

  render () {
    return (
      <View>
        <cell onStartmessage={()=>{}} plugid={this.state.configId}  />
      </View>
    )
  }
}

