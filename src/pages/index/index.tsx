import React, { useState, useRef, useEffect } from 'react'
import { View, Text, Image } from '@tarojs/components'
import Taro, { useReady, useDidShow } from '@tarojs/taro'
import './index.scss'
import Logo from '../../static/image/logo.png'
import { autoLogin } from '@/api/login'
import { getBankList } from '@/api/common'
import usePageStatus from '@/hooks/usePageStatus'
import useGetBankList from '@/hooks/useGetBankList'

const Index = () => {
  const [data, setData] = useState<Array<any>>([])

  const [isLogin, setIsLogin] = useState(false)

  const { getPageStatus } = usePageStatus()

  const { getBankData } = useGetBankList()
  const goToLogin = () => {
    Taro.navigateTo({
      url: '/pages/login/index',
    })
  }

  useReady(() => {
    // Taro.clearStorageSync()
    noPwdLogin()
  })

  useDidShow(() => {
    if (isLogin) {
      getPageStatus()
    }
  })

  // 自动登录
  const noPwdLogin = async () => {
    Taro.removeStorageSync('cookie')
    await Taro.login({
      success: async (res) => {
        try {
          const response: Response = await autoLogin(res.code)
          if (response.success) {
            setIsLogin(true)
            Taro.showToast({
              title: '登录成功',
            }).then(() => {
              getPageStatus()
            })
          } else {
            Taro.redirectTo({
              url: '/pages/login/index',
            })
          }
        } catch (error) {
          console.log('home', error)
        }
      },
    })
  }

  const searchPicker = useRef<any>()

  const getBank = async () => {
    try {
      const result = await getBankList()
      if (result.success) {
        setData(result.data)
      } else {
        Taro.showToast({
          title: result.message,
          icon: 'none',
        })
      }
    } catch (error) {
      console.log(error)
    }
  }

  const handleShow = () => {
    Taro.navigateTo({
      url: '/pages/register/registerMerchant/companyRegister/companyRegisterBank',
    })
    // searchPicker.current.open(data)
  }

  const goToPerson = () => {
    Taro.navigateTo({
      url: '/pages/register/realNameMerchant/realName',
    })
  }

  return (
    <View className="index ">
      <View className="logo" onClick={goToLogin}>
        <Image src={Logo} mode="scaleToFill" />
      </View>
    </View>
  )
}

export default Index
