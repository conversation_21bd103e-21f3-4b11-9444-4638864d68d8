import { useState } from 'react'
import { View, Image, Input, Button, Text } from '@tarojs/components'
import { AtInput, AtIcon } from 'taro-ui'
import usePageStatus from '@/hooks/usePageStatus'
import { useGetCode } from '@/hooks/useGetCode'
import { autoLogin } from '@/api/login'
import Logo from '@/static/image/logo.png'
import CopyRight from '@/static/image/copyright.png'
import Taro, { useReady, useDidShow } from '@tarojs/taro'
import { getCheckCode, getYear, accountLogin, phoneLogin } from '@/api/login'
import './index.scss'

function Index() {
  const [mobileNum, setMobileNum] = useState()
  const [code, setCode] = useState()
  const [loginName, setLoginName] = useState()
  const [password, setPassword] = useState()
  const [dateTime, setDateTime] = useState(new Date().getFullYear())
  const [loginTypeText, setLoginTypeText] = useState('短信验证码登录')
  const [type, setType] = useState(true)
  const [disabled, setDisabled] = useState(false)
  const { count, startInterval } = useGetCode()
  const { getPageStatus } = usePageStatus()

  useReady(async () => {

  })

  useDidShow(() => {
    Taro.hideHomeButton()
  })



  // 获取验证码
  const getMsg = async () => {
    try {
      if (!mobileNum) return Taro.showToast({ title: '请输入手机号', icon: 'none' })
      if (!verifyPhone(mobileNum))
        return Taro.showToast({ title: '请输入正确的手机号', icon: 'none' })
      if (count === -1) {
        const response: Response = await getCheckCode(mobileNum)
        if (response.success) {
          await Taro.showToast({
            title: '验证码已发送',
          })
          getCode()
        } else {
          await Taro.showToast({
            title: response.message,
            icon: 'none',
          })
        }
      } else {
        await Taro.showToast({
          title: '倒计时结束后再发送',
        })
      }
    } catch (error) {
      console.error(error)
    }
  }

  // 校验手机号
  const verifyPhone = (phone: string) => {
    let reg = /^[1][3,4,5,7,8,9][0-9]{9}$/
    return phone && phone.length === 11 && reg.test(phone)
  }

  // 倒计时
  const getCode = () => {
    startInterval()
  }

  // 切换登录方式
  const changeLoginType = () => {
    setType(!type)
    setLoginTypeText(type ? '密码登录' : '短信验证码登录')
    setMobileNum(undefined)
    setCode(undefined)
    setLoginName(undefined)
    setPassword(undefined)
  }

  const handleInput = (e, type) => {
    const { value } = e.detail
    switch (type) {
      case 'code':
        setCode(value)
        break
      case 'mobile':
        setMobileNum(value)
        break
      case 'loginName':
        setLoginName(value)
        break
      case 'password':
        setPassword(value)
        break
    }
    return value
  }

  const goRegister = async () => {
    await Taro.redirectTo({
      url: '/pages/register/user/UserRegister',
    })
  }

  // 切换登录方式UI
  const changeLoginView = () => {
    if (!type) {
      return (
        <View className="inputBox">
          <View className="input">
            <AtIcon value="user" color="rgba(0, 0, 0, 0.25)" size={10} />
            <Input
              name="loginName"
              value={mobileNum}
              type="number"
              placeholder-class="placeholder"
              placeholder="请输入手机号码"
              onInput={e => handleInput(e, 'mobile')}
            />
          </View>
          <View className="input code">
            <Input
              type="number"
              value={code}
              onInput={e => handleInput(e, 'code')}
              placeholder-class="placeholder"
              placeholder="请输入短信验证码"
            />
            <View className="getCode">
              <Button hover-class="hover" onClick={getMsg}>
                <Text>{count === -1 ? '获取验证码' : count}</Text>
              </Button>
            </View>
          </View>
        </View>
      )
    } else {
      return (
        <View className="inputBox">
          <View className="input">
            <AtIcon value="user" color="rgba(0, 0, 0, 0.25)" size={10} />
            <Input
              value={loginName}
              placeholder-class="placeholder"
              placeholder="请输入账号"
              onInput={e => handleInput(e, 'loginName')}
            />
          </View>
          <View className="input code">
            <AtIcon value="lock" color="rgba(0, 0, 0, 0.25)" size={10} />
            <Input
              password
              value={password}
              onInput={e => handleInput(e, 'password')}
              placeholder-class="placeholder"
              placeholder="请输入密码"
            />
          </View>
        </View>
      )
    }
  }

  // 登录
  const confirm = async () => {
    try {
      if (type) {
        if (!loginName) return Taro.showToast({ title: '请输入账号', icon: 'none' })
        if (!password) return Taro.showToast({ title: '请输入密码', icon: 'none' })
      } else {
        if (!mobileNum) return Taro.showToast({ title: '请输入手机号', icon: 'none' })
        if (!verifyPhone(mobileNum))
          return Taro.showToast({ title: '请输入正确的手机号', icon: 'none' })
        if (!code) return Taro.showToast({ title: '请输入验证码', icon: 'none' })
      }

      let params = {
        loginName: type ? loginName : undefined,
        password: type ? password : undefined,
        phone: !type ? mobileNum : undefined,
        smsVerifyCode: !type ? code : undefined,
      }
      setDisabled(true)
      await Taro.login({
        success: async res => {
          if (res.code) {
            let response: Response
            if (type) {
              response = await accountLogin(params)
            } else {
              response = await phoneLogin(params)
            }
            if (response.success) {
              setDisabled(false)
              await getPageStatus()
            } else {
              await Taro.showToast({
                title: response.message,
                icon: 'none',
              })
              setDisabled(false)
            }
          }
        },
      })
    } catch (error) {
      await Taro.showToast({
        title: error,
        icon: 'none',
      })
    }
  }

  return (
    <View className="register">
      <View className="logo">
        <Image src={Logo} mode="scaleToFill" />
      </View>
      {changeLoginView()}
      <View className="changeLoginType">
        <Text onClick={changeLoginType}>{loginTypeText}</Text>
      </View>
      <View className="submit">
        <Button disabled={disabled} onClick={confirm}>
          登录
        </Button>
      </View>
      <View className="bg">
        {/*<View className="bottom-tip">还没有注册客户中心</View>*/}
        {/*<View className="bottom-register" onClick={goRegister}>*/}
        {/*  立即注册*/}
        {/*</View>*/}
        <View className="bottom-copyright">
          Copyright
          <Image src={CopyRight} mode="scaleToFill" />
          {dateTime}燕文物流
        </View>
      </View>
    </View>
  )
}

export default Index
