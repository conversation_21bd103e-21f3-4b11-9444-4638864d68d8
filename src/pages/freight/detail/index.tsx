import { ScrollView, View } from '@tarojs/components'
import { observer, inject } from 'mobx-react'
import { Freight } from '@/store/modules/freight'
import { useMount } from 'ahooks'
import { useState } from 'react'
import ScrollTabs from '@/components/ScrollTabs'
import RichTextView from '@/components/RichTextView'
import { useDidHide } from '@tarojs/taro'
import CustomMenu from '@/components/CustomMenu'
import Taro from '@tarojs/taro'

const Index = ({ freight }: { freight: Freight }) => {
  const [detailData, setDetailData] = useState<Freight.Item>()
  const [tabValue, setTabValue] = useState<string>('1')

  const tabList = [
    { title: '产品说明', value: '1' },
    ...(detailData?.topcRemarks?.length ? [{ title: '国家要求', value: '2' }] : []),
  ]

  useMount(() => {
    initialFunc()
  })

  useDidHide(() => {
    freight.setDetailData({})
  })

  function initialFunc() {
    setDetailData(freight.detailData)
  }

  const productDescriptionRender = () => {
    return (
      <View className="py-[13px] ">
        <View className="text-[#4aac11]  flex flex-row items-center gap-[8px]">
          <View className="w-[2px] h-[16px] bg-[#4aac11]" />
          <View>参考时效</View>
        </View>
        <View className="text-[#3D3D3D] py-[11px] text-[14px] leading-[24px]">
          <RichTextView content={detailData?.remark?.referAging} />
        </View>
        <View className="text-[#4aac11]  flex flex-row items-center gap-[8px]">
          <View className="w-[2px] h-[16px] bg-[#4aac11]" />
          <View>价格构成</View>
        </View>
        <View className="text-[#3D3D3D] py-[11px] text-[14px] leading-[24px]">
          <RichTextView content={detailData?.remark?.priceStructure} />
        </View>
        <View className="text-[#4aac11]  flex flex-row items-center gap-[8px]">
          <View className="w-[2px] h-[16px] bg-[#4aac11]" />
          <View>计费方式</View>
        </View>
        <View className="text-[#3D3D3D] py-[11px] text-[14px] leading-[24px]">
          <RichTextView content={detailData?.remark?.billingWay} />
        </View>
        <View className="text-[#4aac11]  flex flex-row items-center gap-[8px]">
          <View className="w-[2px] h-[16px] bg-[#4aac11]" />
          <View>走货属性</View>
        </View>
        <View className="text-[#3D3D3D] py-[11px] text-[14px] leading-[24px]">
          <RichTextView content={detailData?.remark?.goodsAttribute} />
        </View>
        <View className="text-[#4aac11]  flex flex-row items-center gap-[8px]">
          <View className="w-[2px] h-[16px] bg-[#4aac11]" />
          <View>申报价值</View>
        </View>
        <View className="text-[#3D3D3D] py-[11px] text-[14px] leading-[24px]">
          <RichTextView content={detailData?.remark?.declaredValue} />
        </View>
        <View className="text-[#4aac11]  flex flex-row items-center gap-[8px]">
          <View className="w-[2px] h-[16px] bg-[#4aac11]" />
          <View>重量要求</View>
        </View>
        <View className="text-[#3D3D3D] py-[11px] text-[14px] leading-[24px]">
          <RichTextView content={detailData?.remark?.weightRequirement} />
        </View>
        <View className="text-[#4aac11]  flex flex-row items-center gap-[8px]">
          <View className="w-[2px] h-[16px] bg-[#4aac11]" />
          <View>包装尺寸</View>
        </View>
        <View
          className={`text-[${
            detailData?.remark?.productCode == '801' ? 'red' : '#3D3D3D'
          }] py-[11px] text-[14px]`}
        >
          <RichTextView content={detailData?.remark?.packingSize} />
        </View>
        <View className="text-[#4aac11]  flex flex-row items-center gap-[8px]">
          <View className="w-[2px] h-[16px] bg-[#4aac11]" />
          <View>派送地址要求</View>
        </View>
        <View className="text-[#3D3D3D] py-[11px] text-[14px] leading-[24px]">
          <RichTextView content={detailData?.remark?.deliveryRequirements} />
        </View>
        <View className="text-[#4aac11]  flex flex-row items-center gap-[8px]">
          <View className="w-[2px] h-[16px] bg-[#4aac11]" />
          <View>退件重派</View>
        </View>
        <View className="text-[#3D3D3D] py-[11px] text-[14px] leading-[24px]">
          <RichTextView content={detailData?.remark?.toResend} />
        </View>
        <View className="text-[#4aac11]  flex flex-row items-center gap-[8px]">
          <View className="w-[2px] h-[16px] bg-[#4aac11]" />
          <View>保险服务</View>
        </View>
        <View className="text-[#3D3D3D] py-[11px] text-[14px] leading-[24px]">
          <RichTextView content={detailData?.remark?.insurance} />
        </View>
        <View className="text-[#4aac11]  flex flex-row items-center gap-[8px]">
          <View className="w-[2px] h-[16px] bg-[#4aac11]" />
          <View>赔偿标准</View>
        </View>
        <View className="text-[#3D3D3D] py-[11px] text-[14px] leading-[24px]">
          <RichTextView content={detailData?.remark?.compensation} />
        </View>
        <View className="text-[#4aac11]  flex flex-row items-center gap-[8px]">
          <View className="w-[2px] h-[16px] bg-[#4aac11]" />
          <View>查询网址</View>
        </View>
        <View className="text-[#3D3D3D] py-[11px] text-[14px] leading-[24px]">
          <RichTextView content={detailData?.remark?.queryUrl} />
        </View>
        <View className="text-[#4aac11]  flex flex-row items-center gap-[8px]">
          <View className="w-[2px] h-[16px] bg-[#4aac11]" />
          <View>其他要求</View>
        </View>
        <View className="text-[#3D3D3D] py-[11px] text-[14px] leading-[24px]">
          {detailData?.remark?.otherRequirements == null ? (
            '暂无'
          ) : (
            <RichTextView content={detailData?.remark?.otherRequirements} />
          )}
        </View>
      </View>
    )
  }

  const countryRequirementRender = () => {
    return (
      <View className="py-[13px] ">
        <View className="text-[#4aac11]  flex flex-row items-center gap-[8px]">
          <View className="w-[2px] h-[16px] bg-[#4aac11]" />
          <View>国家</View>
        </View>
        <View className="text-[#3D3D3D] py-[11px] text-[14px] leading-[24px]">
          <RichTextView content={detailData?.topcRemarks?.[0]?.countryName} />
        </View>
        <View className="text-[#4aac11]  flex flex-row items-center gap-[8px]">
          <View className="w-[2px] h-[16px] bg-[#4aac11]" />
          <View>参考时效 (自然日)</View>
        </View>
        <View className="text-[#3D3D3D] py-[11px] text-[14px] leading-[24px]">
          <RichTextView content={detailData?.topcRemarks?.[0]?.referAging} />
        </View>
        <View className="text-[#4aac11]  flex flex-row items-center gap-[8px]">
          <View className="w-[2px] h-[16px] bg-[#4aac11]" />
          <View>特殊要求</View>
        </View>
        <View className="text-[#3D3D3D] py-[11px] text-[14px] leading-[24px]">
          <RichTextView content={detailData?.topcRemarks?.[0]?.specialRequirement} />
        </View>
        <View className="text-[#4aac11]  flex flex-row items-center gap-[8px]">
          <View className="w-[2px] h-[16px] bg-[#4aac11]" />
          <View>申报价值</View>
        </View>
        <View className="text-[#3D3D3D] py-[11px] text-[14px] leading-[24px]">
          <RichTextView content={detailData?.topcRemarks?.[0]?.declaredValue} />
        </View>
        <View className="text-[#4aac11]  flex flex-row items-center gap-[8px]">
          <View className="w-[2px] h-[16px] bg-[#4aac11]" />
          <View>税费征收说明</View>
        </View>
        <View className="text-[#3D3D3D] py-[11px] text-[14px] leading-[24px]">
          <RichTextView content={detailData?.topcRemarks?.[0]?.rateDesc} />
        </View>
        <View className="text-[#4aac11]  flex flex-row items-center gap-[8px]">
          <View className="w-[2px] h-[16px] bg-[#4aac11]" />
          <View>重量要求</View>
        </View>
        <View className="text-[#3D3D3D] py-[11px] text-[14px] leading-[24px]">
          <RichTextView content={detailData?.topcRemarks?.[0]?.weightRequirement} />
        </View>
        <View className="text-[#4aac11]  flex flex-row items-center gap-[8px]">
          <View className="w-[2px] h-[16px] bg-[#4aac11]" />
          <View>包装尺寸</View>
        </View>
        <View className="text-[#3D3D3D] py-[11px] text-[14px] leading-[24px]">
          <RichTextView content={detailData?.topcRemarks?.[0]?.packingSize} />
        </View>
        <View className="text-[#4aac11]  flex flex-row items-center gap-[8px]">
          <View className="w-[2px] h-[16px] bg-[#4aac11]" />
          <View>派送地址要求</View>
        </View>
        <View className="text-[#3D3D3D] py-[11px] text-[14px] leading-[24px]">
          <RichTextView content={detailData?.topcRemarks?.[0]?.deliveryRequirement} />
        </View>
        <View className="text-[#4aac11]  flex flex-row items-center gap-[8px]">
          <View className="w-[2px] h-[16px] bg-[#4aac11]" />
          <View>退件重派</View>
        </View>
        <View className="text-[#3D3D3D] py-[11px] text-[14px] leading-[24px]">
          <RichTextView content={detailData?.topcRemarks?.[0]?.toResend} />
        </View>
      </View>
    )
  }

  const handleBack = () => {
    Taro.navigateBack()
  }

  return (
    <View>
      {/* <CustomMenu title="产品详情" onClick={handleBack} /> */}
      <View className="flex flex-row justify-between bg-[#fff] py-[13px] px-[12px]">
        {detailData?.productInfo?.productName}
      </View>
      <ScrollView scrollY enhanced showScrollbar={false} className="bg-[#f3f3f3] h-[75vh]">
        <View className="p-[10px] pt-0">
          <ScrollTabs
            className="text-[16px]"
            tabList={tabList}
            tabValue={tabValue}
            onTabChange={(value) => {
              setTabValue(value)
            }}
          />
          <View
            //   style={{ height: 'calc(100vh - 70px)' }}
            className="bg-[#fff] px-[12px]"
          >
            {tabValue === '1' ? productDescriptionRender() : countryRequirementRender()}
          </View>
        </View>
      </ScrollView>
    </View>
  )
}

export default inject('freight')(observer(Index))
