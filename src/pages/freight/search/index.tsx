import { useRef, useState } from 'react'
import { Button, Input, View } from '@tarojs/components'
import Form, { FormRefInterface } from '@/components/Form'
import SearchSelectPicker from '@/components/SearchSelectPicker'
import './index.scss'
import { useMount, useUpdateEffect } from 'ahooks'
import { getCountries, getWarehouses, chargePrice } from '@/api/freight'
import Taro from '@tarojs/taro'
import { observer, inject } from 'mobx-react'
import { Freight } from '@/store/modules/freight'
import CustomMenu from '@/components/CustomMenu'
import { useUnmount } from 'ahooks'
import { productAttributesList,productTypeList } from '@/utils/commonConstant'



const Index = ({ freight }: { freight: Freight }) => {
  const formRef = useRef<FormRefInterface>()
  const [length, setLength] = useState('')
  const [width, setWidth] = useState('')
  const [high, setHigh] = useState('')
  const [weight, setWeight] = useState('')
  const [countries, setCountries] = useState<Freight.Country[]>([])
  const [warehouses, setWarehouses] = useState<Freight.Warehouse[]>([])
  const [pickWarehouseId, setPickWarehouseId] = useState()
  useMount(() => {
    initialFunction()
  })

  useUpdateEffect(() => {
    if (pickWarehouseId && warehouses.length > 0) {
      formRef?.current?.setFieldsValue({
        cityId: warehouses.find((val) => val.code === pickWarehouseId)?.code,
        ...freight?.headInformation,
      })
    }
  }, [pickWarehouseId, warehouses])

  useUnmount(() => {
    freight.setHeadInformation({})
  })

  const initialFunction = () => {
    fetchGetCountries()
    fetchGetWarehouses()
  }

  const fetchGetCountries = async () => {
    const response = await getCountries()
    if (response.success) {
      setCountries(response.data)
    }
  }

  const fetchGetWarehouses = async () => {
    const response = await getWarehouses()
    if (response.success) {
      setWarehouses(
        response.data?.warehouses?.map((val) => ({
          ...val,
          label: `${val?.name?.replace('燕文', '')}`,
          value: val.code,
        }))
      )
      setPickWarehouseId(response.data?.pickWarehouseId)
    }
  }

  const sizeRender = () => {
    return (
      <View>
        <View className="flex items-center" style={{ borderBottom: '1px solid #DCDCDC' }}>
          <View className="py-[11px] pl-[5px] flex-1" style={{ borderBottom: '1px solid #eeeeee' }}>
            <View className="flex items-center">
              <View className={`text-[14px] flex-1`}>尺寸（cm）</View>
              <View className="flex-2 flex justify-around">
                <Input
                  className="text-center flex-1 bg-[#eaeaea] text-[12px] mr-[5px]"
                  placeholder="长"
                  type="digit"
                  value={length}
                  onInput={(event) => {
                    setLength(event.detail.value)
                  }}
                  placeholderClass="text-[#bebbbb]"
                />
                <Input
                  className="text-center flex-1 bg-[#eaeaea] text-[12px] mr-[5px]"
                  placeholder="宽"
                  value={width}
                  onInput={(event) => {
                    setWidth(event.detail.value)
                  }}
                  type="digit"
                  placeholderClass="text-[#bebbbb]"
                />
                <Input
                  className="text-center flex-1 bg-[#eaeaea] text-[12px]"
                  placeholder="高"
                  value={high}
                  onInput={(event) => {
                    setHigh(event.detail.value)
                  }}
                  type="digit"
                  placeholderClass="text-[#bebbbb]"
                />
              </View>
            </View>
          </View>
        </View>
      </View>
    )
  }

  const handleReset = () => {
    formRef?.current?.resetFields()
    setHigh('')
    setWidth('')
    setLength('')
    setWeight('')
  }

  const handleSubmit = async () => {
    try {
      await Taro.showLoading({
        title: '查询中',
      })
      const values = await formRef?.current?.validateFieldsValue()
      if (!weight) {
        Taro.showToast({
          title: '请输入实际重量',
          icon: 'none',
        })
        return
      }
      const params = {
        ...values,
        length,
        width,
        high,
        weight,
        productTypes: values?.productTypes?.join(','),
      }
      const response = await chargePrice(params)

      if (response.success) {
        Taro.hideLoading()
        freight.setDataResult(response.data)
        freight.setHeadInformation({
          ...params,
          cityName: warehouses.find((val) => val.code === params.cityId)?.name,
          countryName: countries.find((val) => val.id === params.countryId)?.nameCh,
        })
        Taro.navigateTo({
          url: '/pages/freight/list/index',
        })
      }
    } catch (error) {}
  }

  const handleBack = () => {
    handleReset()
    Taro.navigateBack()
  }

  return (
    <View catchMove className="bg-content">
      {/* <CustomMenu title="运费试算" onClick={handleBack} /> */}
      <View className="pt-[10px] px-[10px]">
        <Form formRef={formRef}>
          <View className="bg-[#fff] rounded-[8px] px-[11px]">
            <Form.Item
              label={
                <View className="flex  items-center">
                  <View className="text-[12px] p-[4px] rounded-sm text-[#fff] bg-[#43a046] mr-[3px]">
                    发
                  </View>
                  <View className="freight-form-label-required">出发地</View>
                </View>
              }
              name="cityId"
              rules={[
                {
                  required: true,
                  message: '请选择出发地',
                },
              ]}
            >
              <SearchSelectPicker title="请选择出发地" options={warehouses} />
            </Form.Item>
            <Form.Item
              label={
                <View className="flex  items-center">
                  <View className="text-[12px] p-[4px] rounded-sm text-[#fff] bg-[#182e9c] mr-[3px]">
                    收
                  </View>
                  <View className="freight-form-label-required">目的地</View>
                </View>
              }
              name="countryId"
              rules={[
                {
                  required: true,
                  message: '请选择目的地',
                },
              ]}
            >
              <SearchSelectPicker
                title="请选择目的地"
                options={countries.map((val) => ({
                  ...val,
                  label: `${val?.nameCh}/${val?.code}/${val?.nameEn}`,
                  value: val.id,
                }))}
              />
            </Form.Item>
            <Form.Item label="邮编" name="postCode" noStyle>
              <Input type="text" placeholder="请输入目的地邮编" className="text-right" />
            </Form.Item>
          </View>
          <View className="bg-[#fff] rounded-[8px] px-[6px] mt-[10px]">
            <View className={`flex items-center `} style={{ borderBottom: '1px solid #DCDCDC' }}>
              <View className={`py-[11px] pl-[5px] flex-1 items-center`}>
                <View className={`flex flex-row items-center`}>
                  <View className={`text-[14px] flex-1`}>
                    <View className={`freight-form-label-required`}>实际重量</View>
                  </View>

                  <View className="flex flex-row gap-[8px] items-center">
                    <Input
                      placeholder="请输入实际重量"
                      type="digit"
                      className="text-right text-[14px]"
                      value={weight}
                      onInput={(e) => {
                        setWeight(e.detail.value)
                        return e.detail.value
                      }}
                    />
                    <View className="text-[#5f5d5d] text-[14px] px-[5px]">克</View>
                  </View>
                </View>
              </View>
            </View>
            {/* <Form.Item
            label={<View className="">实际重量</View>}
            name="weight"
            rules={[
              {
                required: true,
                message: '请输入实际重量',
              },
            ]}
          >
            <Input
              type="digit"
              placeholder="请输入"
              className="flex justify-end text-right weight-after"
            />
          </Form.Item> */}
            {sizeRender()}
            <Form.Item type="picker" label="货品属性" name="productAttributes">
              <SearchSelectPicker
                title="请选择货品属性"
                options={productAttributesList}
              />
            </Form.Item>
            <Form.Item type="picker" multiple label="产品类型" name="productTypes" noStyle>
              <SearchSelectPicker
                title="请选择产品类型"
                multiple
                options={productTypeList}
              />
            </Form.Item>
          </View>
          <View className="flex mt-[10px]">
            <Button
              style={{ lineHeight: 2.8, border: '1px solid #b2b2b2' }}
              className="w-[35%] h-[40px] rounded-[25px] bg-[#FFFFFF] text-[14px] text-[#b2b2b2]"
              onClick={() => handleReset()}
            >
              重置
            </Button>
            <Button
              style={{ lineHeight: 2.8 }}
              className="w-[55%] h-[40px] rounded-[25px] text-[14px]"
              type="primary"
              onClick={handleSubmit}
            >
              查询
            </Button>
          </View>
        </Form>
      </View>
    </View>
  )
}

export default inject('freight')(observer(Index))
