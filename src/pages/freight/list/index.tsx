import { Button, Image, ScrollView, View } from '@tarojs/components'
import { observer, inject } from 'mobx-react'
import { useRef, useState } from 'react'
import { useMount } from 'ahooks'
import { AtIcon } from 'taro-ui'
import { Freight } from '@/store/modules/freight'
import FlatList from '@/components/FlatList/FlatList'
import Descriptions from '@/components/Descriptions'
import _ from 'lodash'
import { useDidHide } from '@tarojs/taro'
import Taro from '@tarojs/taro'
import CustomMenu from '@/components/CustomMenu'
import IconWeight from '@/static/image/icon-weight.png'
import IconPin from '@/static/image/icon-pin.png'
import IconRight from '@/static/image/icon-right.png'
import PageContainerForm, { FormRefInterface } from '@/components/PageContainerForm'
import SearchSelectPicker from '@/components/SearchSelectPicker'
import Shaixuan from '@/static/image/icon-shaixuan.png'
import { productAttributesList,productTypeList } from '@/utils/commonConstant'
import { chargePrice } from '@/api/freight'

 

const Index = ({ freight }: { freight: Freight }) => {
const formRef = useRef<FormRefInterface>(null)

  const [dataSource, setDataSource] = useState<Freight.Item[]>([])
  const [headInformation, setHeadInformation] = useState<Partial<Freight.chargePriceRequest>>({})
  // const [showPrice, setShowPrice] = useState<'string'>(false)
  // const [showProduct, setShowProduct] = useState<'string'>(false)
  const [priceSort, setPriceSort] = useState<'desc' | 'asc'>('desc')
  const [productSort, setProductSort] = useState<'desc' | 'asc'>('desc')
  const [timeSort, setTimeSort] = useState<'desc' | 'asc'>('desc')
  useMount(() => {
    initialFunc()
  })

  function initialFunc() {
    setDataSource(
      freight.dataResult?.result?.items?.map((v) => {
        let money = Number(_.sumBy(v.expenseItems, 'money').toFixed(2))
        let moneyOfOriginal = Number(_.sumBy(v.expenseItems, 'moneyOfOriginal').toFixed(2))
        return {
          ...v,
          money,
          moneyOfOriginal,
          showDetail: false,
        }
      }) || []
    )
    setHeadInformation(freight.headInformation)
  }

  useDidHide(() => {
    freight.setHeadInformation({})
    freight.setDataResult({})
  })

  function handleSort(type: 'moneyOfOriginal' | 'productType' | 'time') {

    // 确定排序字段
    const field = type === 'moneyOfOriginal' ? 'moneyOfOriginal' :type === 'time' ? 'referAging' : 'productInfo.productShowOrder'

    // 更新排序状态
    let sortState: 'desc' | 'asc'
    if (type === 'moneyOfOriginal') {
      sortState = priceSort
    } else if (type === 'time') {
      sortState = timeSort
    } else {
      sortState = productSort
    }
    const newSortState = sortState === 'desc' ? 'asc' : 'desc'

    if (type === 'moneyOfOriginal') {
      setPriceSort(newSortState)
    } else if (type === 'productType') {
      setProductSort(newSortState)
    } else if (type === 'time') {
      setTimeSort(newSortState)
    }

    // 排序处理
    const getValueByPath = (obj: any, path: string) => {
      const keys = path.split('.')
      let result = obj
      for (const key of keys) {
        result = result?.[key]
      }
      return result ?? 0
    }

    setDataSource(
      dataSource?.sort((a, b): any => {
        if (type === 'productType') {
          // 二级排序逻辑
          const aOrder = getValueByPath(a, 'productInfo.productShowOrder')
          const bOrder = getValueByPath(b, 'productInfo.productShowOrder')
          const sort = newSortState === 'desc' ? bOrder - aOrder : aOrder - bOrder

          if (sort === 0) {
            const aMoney = getValueByPath(a, 'rmbTotalMoney')
            const bMoney = getValueByPath(b, 'rmbTotalMoney')
            return newSortState === 'desc' ? bMoney - aMoney : aMoney - bMoney
          }
          return sort
        } else if (type === 'moneyOfOriginal') {
          // 原有的价格排序逻辑
          const aValue = getValueByPath(a, field)
          const bValue = getValueByPath(b, field)
          return newSortState === 'desc' ? aValue - bValue : bValue - aValue
        } else if (type === 'time') {
          const aTime = getValueByPath(a, 'referAging')
          const bTime = getValueByPath(b, 'referAging')

          const SORT_ORDER = {
            AFTER: 1,
            BEFORE: -1,
            EQUAL: 0,
          } as const

          // 数据例子: aTime = 10-20天  现在需要通过10-20天 转换成 10 和 20
          // 然后根据这个需求来排序
          // 升序就是这么排
          // 1、按照区间左侧参数排；
          // 2、若区间左侧参数一样则右侧参数升序排
          // 3、仅有一个参数的默认为左侧参数升序排
          // 4、无时效参数的则排在最后
          // Parse time ranges like "10-20天" or "5天"
          // 时间解析函数
          const parseTimeRange = (timeStr?: string) => {
            if (!timeStr) return { isValid: false, min: 0, max: 0 }

            // 使用正则匹配数字部分
            const matches = timeStr.match(/(\d+)(?:-(\d+))?/)
            if (!matches) return { isValid: false, min: 0, max: 0 }

            const [, start, end] = matches
            const min = parseInt(start, 10) || 0
            const max = end ? parseInt(end, 10) || min : min

            return { isValid: true, min, max }
          }

          const aRange = parseTimeRange(aTime)
          const bRange = parseTimeRange(bTime)

          // 无效数据排序处理
          if (!aRange.isValid) return SORT_ORDER.AFTER
          if (!bRange.isValid) return SORT_ORDER.BEFORE

          // 主排序比较
          const primaryCompare = newSortState === 'desc' ? bRange.min - aRange.min : aRange.min - bRange.min

          // 次要排序比较
          const secondaryCompare = newSortState === 'desc' ? bRange.max - aRange.max : aRange.max - bRange.max

          return primaryCompare || secondaryCompare
        }
      }) ?? []
    )
  }

  function handleItemRender(item: Freight.Item, index: number) {
    return (
      <View className="px-[8px] pb-[10px]">
        <View className="bg-[#fff] rounded-[8px] px-[2px]">
          <View
            className="text-[16px] px-[10px] py-[16px] flex flex-row items-center gap-[10px]"
            style={{
              borderBottom: '1px solid #e4e4e4',
            }}
          >
            <View>{item.productInfo?.productName}</View>
            {item?.recommendProduct === 1 && (
              <View className="px-[4px] py-[2px] rounded-[2px] bg-[#e83c3c] text-[#fff] text-[12px]">
                荐
              </View>
            )}
          </View>
          <View className="px-[10px] py-[10px]">
            <View className="flex flex-row items-center gap-[10px] text-[14px]">
              <View className="text-[#157c5d]">{item?.trackingInfo}</View>
              <View className="text-[#999] text-[12px]">|</View>
              <View className="text-[#2f6d0a]">{item?.productInfo?.ywproductGenealogy}</View>
              <View className="text-[#999] text-[12px]">|</View>
              <View className="text-[#867405]">{item?.productInfo?.itemAttribute}</View>
            </View>
          </View>
          <View className="flex flex-row items-end px-[60px] py-[10px] justify-between">
            <View className="flex flex-row items-end gap-[8px]">
              <View className="text-[#333] text-[20px]">
                {item?.referAging?.split('天')?.[0] || ''}
              </View>
              <View className="text-[#333] text-[12px]">{item?.referAging ? '天' : ''}</View>
            </View>
            <View className="flex flex-row items-end gap-[8px]">
              <View className="text-[#529b26] text-[20px]">{item?.money}</View>
              <View className="text-[#333] text-[12px]">元</View>
              <View
                className="text-[#c8c8c8] text-[12px] flex flex-row items-end gap-[2px]"
                onClick={() => {
                  item.showDetail = !item.showDetail
                  setDataSource(dataSource.map((v, i) => (i === index ? item : v)))
                }}
              >
                <View>明细</View>
                <AtIcon
                  value={item.showDetail ? 'chevron-up' : 'chevron-down'}
                  size="5"
                  color="#c8c8c8"
                />
              </View>
            </View>
          </View>
          <View className="mx-[15px] h-[1px] bg-[#e4e4e4]" />
          {item.showDetail && (
            <View className="px-[10px] py-[0px]">
              <View className="px-[5px] text-[12px]">
                <Descriptions column={2}>
                  <Descriptions.Item label="资费">{item?.moneyOfOriginal}元</Descriptions.Item>
                  <Descriptions.Item label="折扣">{item?.discountRate}</Descriptions.Item>
                  {item?.basicFreight != 0 && (
                    <Descriptions.Item label="基础费">{item?.basicFreight}元</Descriptions.Item>
                  )}
                  {item?.trunkFreight != 0 && (
                    <Descriptions.Item label="干线费">{item?.trunkFreight}元</Descriptions.Item>
                  )}
                  {item?.fuelCost != 0 && (
                    <Descriptions.Item label="燃油费">{item?.fuelCost}元</Descriptions.Item>
                  )}
                  {item?.additionalCharge != 0 && (
                    <Descriptions.Item label="附加费">{item?.additionalCharge}元</Descriptions.Item>
                  )}
                </Descriptions>
              </View>
              {/* <View className="text-[#333] text-[14px]">{item?.productInfo?.productDesc}</View> */}
            </View>
          )}
          <View className="px-[15px] py-[10px] flex flex-row items-center">
            <View className="text-[12px] pr-[30px] text-[#868181] flex-3">
              <Descriptions column={2}>
                <Descriptions.Item label="计费重量">{item?.calcWeight}克</Descriptions.Item>
                {item?.isThrowweight && (
                  <Descriptions.Item label="计泡系数">{item?.throwWeightFactor}</Descriptions.Item>
                )}
                <Descriptions.Item label="燃油费率">{item?.productFuelRate}</Descriptions.Item>
              </Descriptions>
            </View>
            <Button
              className="text-[#000]  rounded-[57px] flex-1"
              style={{ fontSize: '12px' }}
              type="primary"
              onClick={() => {
                freight.setDetailData(item)
                Taro.navigateTo({
                  url: '/pages/freight/detail/index',
                })
              }}
            >
              查看详情
            </Button>
          </View>
        </View>
      </View>
    )
  }

  const handleBack = () => {
    Taro.navigateBack()
  }


  async function handleSubmit(values: any) {
    try {
      await Taro.showLoading({
        title: '查询中',
      })
      const params ={
        ...freight.headInformation,
        ...values,
        productTypes: values?.productTypes?.join(','),
      }
      const response = await chargePrice(params)
      if (response.success) {
        Taro.hideLoading()
        setDataSource(response.data?.result?.items?.map((v) => {
          let money = Number(_.sumBy(v.expenseItems, 'money').toFixed(2))
          let moneyOfOriginal = Number(_.sumBy(v.expenseItems, 'moneyOfOriginal').toFixed(2))
          return {
            ...v,
            money,
            moneyOfOriginal,
            showDetail: false,
          }
        }) || [])
      }
    } catch (error) {
      console.log(error)
    }
  }

  return (
    <View catchMove>
      {/* <CustomMenu title="产品列表" onClick={handleBack} /> */}
      <View className="bg-content" catchMove>
        <View className="flex flex-row justify-between bg-[#fff] py-[13px] px-[12px]" onClick={handleBack}>
          <View className="flex flex-row gap-[10px] items-center">
            <Image src={IconPin} className="w-[11px] h-[11px]" />
            <View className="text-[16px] text-[#333] font-bold">{headInformation?.cityName}</View>
            <Image src={IconRight} className="w-[26px] h-[8px]" />
            <View className="text-[16px] text-[#333] font-bold">
              {headInformation?.countryName}
            </View>
          </View>
          <View className="flex flex-row gap-[10px] items-center">
            <Image src={IconWeight} className="w-[15px] h-[14px]" />
            <View className="text-[16px] text-[#333] font-bold">{headInformation?.weight}克</View>
          </View>
        </View>
        <View className="py-[13px] px-[12px] flex flex-row items-center gap-[15px] text-[14px]">
          {/* <View
            className="flex flex-row gap-[8px] items-center"
            onClick={() => handleSort('productType')}
          >
            <View>产品名称</View>
            <AtIcon
              value={productSort === 'desc' ? 'chevron-down' : 'chevron-up'}
              size="8"
              color="#333"
            />
          </View> */}
          <View
            className="flex flex-row gap-[8px] items-center"
            onClick={() => handleSort('moneyOfOriginal')}
          >
            <View>运费最低</View>
            <AtIcon
              value={priceSort === 'desc' ? 'chevron-up' : 'chevron-down'}
              size="8"
              color="#333"
            />
          </View>
          <View className="flex flex-row gap-[8px] items-center" onClick={() => handleSort('time')}>
            <View>时效最快</View>
            <AtIcon
              value={timeSort === 'desc' ?'chevron-down'  : 'chevron-up'}
              size="8"
              color="#333"
            />
          </View>
          <View
          className="flex"
          onClick={() => {
            formRef?.current?.open()
          }}
        >
          <Image className="w-[13px] h-[13px]" src={Shaixuan} />

          <View className="ml-1 text-[14px] text-[#8D8D8D]">筛选</View>
        </View>
        </View>
        <FlatList
          className="h-[75vh]"
          itemData={dataSource}
          itemRender={handleItemRender}
          total={dataSource.length}
          refresherEnabled={false}
        />
      </View>
      <PageContainerForm
        formRef={formRef}
        
        onSubmit={async (values) => {
          try {
            handleSubmit(values)
          } catch (error) {
            console.log(error)
          }
        }}
      >
        <PageContainerForm.Item multiple label="产品类型" name="productTypes" type="picker">
          <SearchSelectPicker multiple title="请选择产品类型" options={productTypeList} />
        </PageContainerForm.Item>
        <PageContainerForm.Item  label="费用类型" name="productAttributes" type="picker">
          <SearchSelectPicker  title="请选择费用类型" options={productAttributesList} />
        </PageContainerForm.Item>
      </PageContainerForm>
    </View>
  )
}

export default inject('freight')(observer(Index))
