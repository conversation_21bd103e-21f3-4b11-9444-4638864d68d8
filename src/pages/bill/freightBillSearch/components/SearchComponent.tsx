import { Button, Image, Input, View } from '@tarojs/components'
import { AtIcon } from 'taro-ui'
import '../index.scss'
import { useDebounceFn } from 'ahooks'
import PageContainerForm, { FormRefInterface } from '@/components/PageContainerForm'
import { useRef, useState } from 'react'
import MultipleDateTimePicker, { DateFormatType } from '@/components/MultipleDateTimePicker'
import dayjs from 'dayjs'
import SearchSelectPicker from '@/components/SearchSelectPicker'
import Shaixuan from '@/static/image/icon-shaixuan.png'
import Descriptions from '@/components/Descriptions'
import BatchTrackingNumberPopup from '@/components/BatchTrackingNumberPopup'
import PageContainerSelect from '@/components/PageContainerSelect'
import { costTypeList } from '@/utils/commonConstant'

export const lastWeekDay =
  dayjs().day() === 0 ? dayjs().subtract(7, 'day').startOf('week') : dayjs().startOf('week')

export const startTime = lastWeekDay
  // Convert to ISO weekday format
  .subtract(1, 'month')
  .format('YYYY-MM-DD')
export const endTime = lastWeekDay.format('YYYY-MM-DD')
interface Props {
  tabList: Array<{
    key: string
    value: string
  }>
  tabValue: string
  onTabChange: (value: string) => void
  searchValue: string
  setSearchValue: (value: string) => void
  reset?: () => void
  allAbnormalCauseData?: Problem.AbnormalCause[]
  onSubmit: (values: any) => void
  accountValue?: string
  accountData?: { label: string; value: string }[]
  setAccountValue: (value: string) => void
  needAll?: boolean
}

const SearchComponents = (props: Props) => {
  const {
    searchValue,
    setSearchValue,
    reset,
    onSubmit,
    accountValue = undefined,
    accountData,
    setAccountValue,
    needAll = true,
  } = props
  const formRef = useRef<FormRefInterface>()
  const pageContainerSelectRef = useRef<any>()
  const [dateTimes, setDateTimes] = useState<string[]>([startTime, endTime])

  const { run } = useDebounceFn(
    (value: string) => {
      setSearchValue(value)
    },
    { wait: 500 }
  )

  return (
    <View className="bg-[#fff] px-[11px] py-[9px]">
      <View className=" flex justify-between items-center ">
        {/* <View className="flex flex-row justify-start items-center bg-[#f3f3f3] rounded-[50px] h-[30px] w-[276px]">
          <AtIcon value="search" size="7" color="#b7bac4" className="ml-[10px]" />
          <Input
            value={searchValue}
            onInput={(e) => {
              run(e.detail.value)
              if (reset) reset()
            }}
            className="ml-[9px] text-[14px] font-[400] text-[#ccc] w-[100%] text-[left]"
            placeholder="输入运单号/订单号查询"
          ></Input>
        </View> */}
        <BatchTrackingNumberPopup
          placeholder="输入运单号/订单号查询"
          onSubmit={(value) => {
            setSearchValue(value?.length > 0 ? value : undefined)
          }}
        />
        <View
          className="flex"
          onClick={() => {
            formRef?.current?.open()
          }}
        >
          <Image className="w-[13px] h-[13px]" src={Shaixuan} />

          <View className="ml-1 text-[14px] text-[#8D8D8D]">筛选</View>
        </View>
      </View>

      <PageContainerForm
        formRef={formRef}
        initialValue={{
          createTimes: [startTime, endTime],
        }}
        onSubmit={async (values) => {
          try {
            onSubmit(values)
          } catch (error) {
            console.log(error)
          }
        }}
      >
        <View
          className="py-[10px] px-[18px] flex justify-between"
          style={{ borderBottom: '1px solid #DCDCDC' }}
        >
          <View className="text-[14px] text-[#3d3d3d]">
            {accountData?.find((item) => item?.value === accountValue)?.label ??
              `${needAll ? '全部' : ''}`}
          </View>
          <View
            className="text-[14px] text-[#C1BCBC] overflow-hidden text-ellipsis whitespace-nowrap"
            onClick={() => {
              pageContainerSelectRef.current.show()
            }}
          >
            切换制单账号 {`>`}
          </View>
        </View>

        <PageContainerForm.Item
          label="账单日期"
          name="createTimes"
          rules={[{ required: true, message: '请选择账单日期' }]}
        >
          <MultipleDateTimePicker
            dateFormat={DateFormatType.YYYY_MM_DD}
            onChange={(value) => {
              setDateTimes(value)
            }}
          />
        </PageContainerForm.Item>
        <PageContainerForm.Item label="费用类型" name="transType" type="picker">
          <SearchSelectPicker title="请选择费用类型" options={costTypeList} />
        </PageContainerForm.Item>
      </PageContainerForm>
      <PageContainerSelect
        needAll={needAll}
        accountData={accountData}
        value={accountValue}
        nodeRef={pageContainerSelectRef}
        setValue={setAccountValue}
      />
    </View>
  )
}

export default SearchComponents
