import { View, Button } from '@tarojs/components'
import { useState, useMemo } from 'react'
import CustomerNumber from '@/components/CustomerNumber'
import SearchComponents from './components/SearchComponent'
import useGetAccountList from '@/hooks/useGetAccountList'
import FlatList from '@/components/FlatList/FlatList'
import { useMount, useUpdateEffect, useDebounceEffect } from 'ahooks'
import { getNumberList } from '@/api/bill'
import dayjs from 'dayjs'
import Descriptions from '@/components/Descriptions'
import Taro from '@tarojs/taro'
import { Bill } from '@/store/modules/bill'
import { startTime, endTime } from './components/SearchComponent'

//0：未处理 1：已处理 2：全部
const tabList = [
  {
    key: '0',
    value: '待处理',
  },
  {
    key: '1',
    value: '已处理',
  },
  {
    key: '2',
    value: '全部',
  },
]

const Index = ({ bill, scrollHeight }: { bill: Bill; scrollHeight: number }) => {
  const [accountValue, setAccountValue] = useState('all')
  const [searchValue, setSearchValue] = useState<any>() // 搜索框的值
  const [tabValue, setTabValue] = useState('0') // tab选中的值
  const [allAbnormalCauseData, setAllAbnormalCauseData] = useState<
    Problem.AllAbnormalCauseResponse['data']
  >([])
  const [formParams, setFormParams] = useState<Partial<Bill.ListRequestForm>>({})
  const [pageIndex, setPageIndex] = useState(1)
  const [total, setTotal] = useState(0)
  const [data, setData] = useState<Array<Bill.RecordItem>>([])

  const { customerData } = useGetAccountList()

  useMount(() => {})

  useDebounceEffect(
    () => {
      init()
    },
    [formParams, pageIndex, tabValue, accountValue, customerData, searchValue],
    { wait: 300 }
  )

  async function init() {
    try {
      const params = {
        ...formParams,
        waybillNumbers: searchValue ?? undefined,
        page: pageIndex,
        size: 10,
        dateOfBillBegin: formParams?.createTimes
          ? dayjs(formParams.createTimes[0]).format('YYYY-MM-DD')
          : startTime,
        dateOfBillEnd: formParams?.createTimes
          ? dayjs(formParams.createTimes[1]).format('YYYY-MM-DD')
          : endTime,
        transType: formParams?.transType === 'all' ? undefined : formParams?.transType,
        shippingAccounts:
          accountValue === 'all' ? customerData.map((item) => item.value) : [accountValue],
      }
      const response = await getNumberList(params)
      if (response.success) {
        setData([...data, ...(response.data?.records || [])])
        setTotal(response.data?.totalRecord || 0)
      }
    } catch (error) {
      console.log(error)
    }
  }

  function handleItemRender(item: Bill.RecordItem, index: number) {
    function handleGoDetail(item: Bill.RecordItem) {
      bill.setDetailData(item)
      Taro.navigateTo({
        url: '/pages/bill/freightBillDetail/index',
      })
    }

    return (
      <View
        className="mt-[8px] bg-[#fff] rounded-md"
        key={index}
        onClick={() => handleGoDetail(item)}
      >
        <View className="p-[6px]" style={{ borderBottom: '1px solid #F6F4F4' }}>
          <View className="flex justify-between items-center p-[6px]">
            <View className="text-[14px] text-[#3D3D3D]">{item?.waybillNumber}</View>
            <View className="text-[14px] text-[#4AAC0F]">{item?.customerCode}</View>
          </View>
        </View>
        <View className="px-[6px] py-[8px] text-[12px]">
          <Descriptions>
            <Descriptions.Item label="订单号">{item?.orderNumber}</Descriptions.Item>
            <Descriptions.Item label="账单总金额（元）">{item?.money}</Descriptions.Item>
            <Descriptions.Item label="实际重量(G)">{item?.weight}</Descriptions.Item>
            <Descriptions.Item label="计费重量(G)">{item?.calcWeight}</Descriptions.Item>
          </Descriptions>
          <Descriptions column={2}>
            <Descriptions.Item label="账单日期">{item?.dateOfBill}</Descriptions.Item>
            <Descriptions.Item
              contentClassName="text-right"
              childStyle={{ color: '#c1c1c1', maxWidth: '100% !important' }}
            >
              <View onClick={() => handleGoDetail(item)}>{'详情 > '}</View>
            </Descriptions.Item>
          </Descriptions>
        </View>
      </View>
    )
  }

  function reset() {
    setData([])
    setTotal(0)
    setPageIndex(1)
  }

  return (
    <View>
      {/* <CustomerNumber
        accountData={customerData}
        accountValue={accountValue}
        setValue={(value) => {
          reset()
          setAccountValue(value)
        }}
      /> */}
      <SearchComponents
        tabList={tabList}
        tabValue={tabValue}
        onTabChange={(value) => {
          reset()
          setTabValue(value)
        }}
        searchValue={searchValue}
        setSearchValue={(values) => {
          reset()
          setSearchValue(values)
        }}
        onSubmit={(values) => {
          reset()
          setFormParams(values)
        }}
        accountData={customerData}
        accountValue={accountValue}
        setAccountValue={(value) => {
          reset()
          setAccountValue(value)
        }}
      />
      <View className="px-[10px] py-[15px] flex-1">
        <View className="flex justify-between items-center">
          <View className="text-[12px] text-[#B9B1B1] mb-[5px]">查询到{total}条数据</View>
          {/* <Button
            type="primary"
            className="mx-0 rounded-[33px]"
            style={{ fontSize: '12px' }}
            onClick={() => {
              // problem.setDisposeInfoData({
              //   name: 'test',
              //   age: '18',
              //   sex: '男',
              // })
              Taro.navigateTo({
                url: '/pages/problem/dispose/index',
              })
            }}
          >
            批量处理
          </Button> */}
        </View>

        <FlatList
          className="h-[65vh]"
          itemData={data}
          total={total}
          itemRender={handleItemRender}
          onRefresh={async () => {
            await new Promise<any>((resolve) => {
              setTimeout(() => {
                setData([])
                if (pageIndex === 1) {
                  reset()
                  init()
                } else {
                  reset()
                }
                resolve('')
              }, 300)
            })
          }}
          onScrollToLower={async () => {
            if (data.length === total) return
            await new Promise<any>((resolve) => {
              setTimeout(() => {
                setPageIndex(pageIndex + 1)
                resolve('')
              }, 300)
            })
          }}
        />
      </View>
    </View>
  )
}

export default Index
