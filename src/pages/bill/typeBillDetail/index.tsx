import Descriptions from '@/components/Descriptions'
import FlatList from '@/components/FlatList/FlatList'
import { Text, View } from '@tarojs/components'
import { useState } from 'react'
import dayjs from 'dayjs'
import { inject, observer } from 'mobx-react'
import { useMount, useDebounceEffect } from 'ahooks'
import { getTypeBillDetail } from '@/api/bill'
import { windowHeight } from '@/utils'

const Index = (props) => {
  const { bill } = props
  const [detailData, setDetailData] = useState<any>({})
  const [data, setData] = useState<any>([])
  const [total, setTotal] = useState(0)
  const [billWeightunit, setBillWeightunit] = useState<any>()
  const [sumRow, setSumRow] = useState<any>(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)

  const height = windowHeight - 128

  useMount(() => {
    init()
  })

  useDebounceEffect(
    () => {
      // init()
      fetchTypeBillDetail()
    },
    [detailData, currentPage],
    { wait: 300 }
  )

  async function fetchTypeBillDetail() {
    const response = await getTypeBillDetail({
      currentPage,
      pageSize,
      startCalcTime: '',
      endCalcTime: '',
      transType: detailData.transType,
      mainNo: detailData.mainNo,
      waybillNumber: detailData?.waybillNumber ?? '',
    })
    if (response.success) {
      setData([...data, ...(response.data?.rows ?? [])])
      setTotal(response.data?.totalrows ?? 0)
      setBillWeightunit(response.data?.rows?.[0]?.billWeightunit ?? 0)
      setSumRow(response.data?.sumrows ?? 0)
    }
  }

  async function init() {
    setDetailData({ ...bill.typeBillDetailData })
  }

  function handleItemRender(item: any, index: number) {
    function handleTransTypeRender(transType: string) {
      switch (transType) {
        // 应收快件
        case 'TC01':
        case 'BTC01':
        case 'CTC01':
          return detailData?.indicator != 2 ? (
            <Descriptions>
              <Descriptions.Item label="订单号">{item?.orderNumber}</Descriptions.Item>
              <Descriptions.Item label="转单号">{item?.exchangeNumber}</Descriptions.Item>
              <Descriptions.Item label="账单日期">
                {dayjs(item?.calcTime).format('YYYY-MM-DD')}
              </Descriptions.Item>
              <Descriptions.Item label="产品名称">{item?.productName}</Descriptions.Item>
              <Descriptions.Item label="目的地">{item?.regionName}</Descriptions.Item>
              <Descriptions.Item label="计费重量(克)">{item?.calcWeight}</Descriptions.Item>
              {transType !== 'CTC01' && (
                <Descriptions.Item label={`资费(${detailData?.currencyName})`}>
                  {item?.calcPrice}
                </Descriptions.Item>
              )}
              {transType !== 'CTC01' && (
                <Descriptions.Item label={`折后资费(${detailData?.currencyName})`}>
                  {item?.discountPrice}
                </Descriptions.Item>
              )}
              {transType !== 'CTC01' && (
                <Descriptions.Item label={`附加费(${detailData?.currencyName})`}>
                  {item?.acctachPrice}
                </Descriptions.Item>
              )}
              {transType !== 'CTC01' && (
                <Descriptions.Item label={`干线费(${detailData?.currencyName})`}>
                  {item?.moneyTrunk}
                </Descriptions.Item>
              )}
              <Descriptions.Item label={`账单金额(${detailData?.currencyName})`}>
                {item?.arPrice}
              </Descriptions.Item>
            </Descriptions>
          ) : (
            <Descriptions>
              <Descriptions.Item label="订单号">{item?.orderNumber}</Descriptions.Item>
              <Descriptions.Item label="计费时间">
                {dayjs(item?.timeOfCalc).format('YYYY-MM-DD')}
              </Descriptions.Item>
              <Descriptions.Item label="转单号">{item?.exchangeNumber}</Descriptions.Item>
              <Descriptions.Item label="产品名称">{item?.productName}</Descriptions.Item>
              <Descriptions.Item label="国家">{item?.regionName}</Descriptions.Item>
              <Descriptions.Item label="邮编">{item?.postCode}</Descriptions.Item>
              <Descriptions.Item label="件数">{item?.piece}</Descriptions.Item>
              <Descriptions.Item label="实重(g)">{item?.weight}</Descriptions.Item>
              <Descriptions.Item
                label={
                  billWeightunit === ''
                    ? '体积重'
                    : billWeightunit === 'kg'
                    ? '体积重(kg)'
                    : '体积重(g)'
                }
              >
                {item?.dimWeight}
              </Descriptions.Item>
              <Descriptions.Item label="计费重(g)">{item?.calcWeight}</Descriptions.Item>
              <Descriptions.Item label="费用名称">{item?.itemName}</Descriptions.Item>
              <Descriptions.Item label={`折后资费(${detailData?.currencyName})`}>
                {item?.discountPrice}
              </Descriptions.Item>
              <Descriptions.Item label={`金额(${detailData?.currencyName})`}>
                {item?.discountPrice}
              </Descriptions.Item>
            </Descriptions>
          )
        // 应收退件
        case 'TC02':
        case 'BTC02':
        case 'CTC02':
          return detailData?.indicator != 2 ? (
            <Descriptions>
              <Descriptions.Item label="序号">{item?.rowNum}</Descriptions.Item>
              <Descriptions.Item label="订单号">{item?.orderCode}</Descriptions.Item>
              <Descriptions.Item label="账单日期">
                {dayjs(item?.calcTime).format('YYYY-MM-DD')}
              </Descriptions.Item>
              <Descriptions.Item label="产品名称">{item?.productName}</Descriptions.Item>
              <Descriptions.Item label="目的地">{item?.regionName}</Descriptions.Item>
              <Descriptions.Item label="计费重量(g)">{item?.calcWeight}</Descriptions.Item>
              <Descriptions.Item label="赔偿金额">{item?.indemnityTotalPrice}</Descriptions.Item>
              <Descriptions.Item label={`运费(${detailData?.currencyName})`}>
                {item?.arPrice}
              </Descriptions.Item>
            </Descriptions>
          ) : (
            <Descriptions>
              <Descriptions.Item label="序号">{item?.rowNum}</Descriptions.Item>
              <Descriptions.Item label="订单号">{item?.orderCode}</Descriptions.Item>
              <Descriptions.Item label="账单日期">
                {dayjs(item?.calcTime).format('YYYY-MM-DD')}
              </Descriptions.Item>
              <Descriptions.Item label="产品名称">{item?.productName}</Descriptions.Item>
              <Descriptions.Item label="国家">{item?.regionName}</Descriptions.Item>
              <Descriptions.Item label="计费重量(g)">{item?.weight}</Descriptions.Item>
              <Descriptions.Item label="赔偿金额">{item?.indemnityTotalPrice}</Descriptions.Item>
              <Descriptions.Item label={`运费(${detailData?.currencyName})`}>
                {item?.arPrice}
              </Descriptions.Item>
            </Descriptions>
          )
        // 应收赔偿
        case 'TC03':
        case 'BTC03':
        case 'CTC03':
          return detailData?.indicator != 2 ? (
            <Descriptions>
              <Descriptions.Item label="账单日期">
                {dayjs(item?.calcTime).format('YYYY-MM-DD')}
              </Descriptions.Item>
              <Descriptions.Item label="产品名称">{item?.productName}</Descriptions.Item>
              <Descriptions.Item label="目的地">{item?.regionName}</Descriptions.Item>
              <Descriptions.Item label="计费重量(克)">{item?.calcWeight}</Descriptions.Item>
              <Descriptions.Item label={`运费(${detailData?.currencyName})`}>
                {item?.arPrice}
              </Descriptions.Item>
              <Descriptions.Item label={`赔偿金额(${detailData?.currencyName})`}>
                {item?.indemnityTotalPrice}
              </Descriptions.Item>
            </Descriptions>
          ) : (
            <Descriptions>
              <Descriptions.Item label="账单日期">
                {dayjs(item?.calcTime).format('YYYY-MM-DD')}
              </Descriptions.Item>
              <Descriptions.Item label="产品名称">{item?.productName}</Descriptions.Item>
              <Descriptions.Item label="目的地">{item?.regionName}</Descriptions.Item>
              <Descriptions.Item label="计费重量(克)">{item?.calcWeight}</Descriptions.Item>
              <Descriptions.Item label={`运费(${detailData?.currencyName})`}>
                {item?.arPrice}
              </Descriptions.Item>
              <Descriptions.Item label={`赔偿金额(${detailData?.currencyName})`}>
                {item?.indemnityTotalPrice}
              </Descriptions.Item>
            </Descriptions>
          )
        // 二次费用
        case 'TC04':
        case 'BTC04':
        case 'CTC04':
          return detailData?.indicator != 2 ? (
            <Descriptions>
              <Descriptions.Item label="制单账号">{item?.customerCode}</Descriptions.Item>
              <Descriptions.Item label="账单日期">
                {dayjs(item?.calcTime).format('YYYY-MM-DD')}
              </Descriptions.Item>
              <Descriptions.Item label="订单号">{item?.orderCode}</Descriptions.Item>
              <Descriptions.Item label="产品名称">{item?.productName}</Descriptions.Item>
              <Descriptions.Item label="目的地">{item?.regionName}</Descriptions.Item>
              <Descriptions.Item label="费用名称">{item?.itemName}</Descriptions.Item>
              <Descriptions.Item label={`账单金额(${detailData?.currencyName})`}>
                {item?.arPrice}
              </Descriptions.Item>
            </Descriptions>
          ) : (
            <Descriptions>
              <Descriptions.Item label="制单账号">{item?.customerCode}</Descriptions.Item>
              <Descriptions.Item label="账单日期">
                {dayjs(item?.calcTime).format('YYYY-MM-DD')}
              </Descriptions.Item>
              <Descriptions.Item label="订单号">{item?.orderCode}</Descriptions.Item>
              <Descriptions.Item label="产品名称">{item?.productName}</Descriptions.Item>
              <Descriptions.Item label="目的地">{item?.regionName}</Descriptions.Item>
              <Descriptions.Item label="费用名称">{item?.itemName}</Descriptions.Item>
              <Descriptions.Item label={`账单金额(${detailData?.currencyName})`}>
                {item?.arPrice}
              </Descriptions.Item>
            </Descriptions>
          )
        // 应收返点
        case 'TC05':
        case 'BTC05':
        case 'CTC05':
          return detailData?.indicator != 2 ? (
            <Descriptions>
              <Descriptions.Item label="订单号">{item?.orderNumber}</Descriptions.Item>
              <Descriptions.Item label="转单号">{item?.exchangeNumber}</Descriptions.Item>
              <Descriptions.Item label="参考号">{item?.yanwenNumber}</Descriptions.Item>
              <Descriptions.Item label="账单日期">
                {dayjs(item?.calcTime).format('YYYY-MM-DD')}
              </Descriptions.Item>
              <Descriptions.Item label="目的地">{item?.regionName}</Descriptions.Item>
              <Descriptions.Item label="计费重量(克)">{item?.calcWeight}</Descriptions.Item>
              <Descriptions.Item label="产品名称">{item?.productName}</Descriptions.Item>
              <Descriptions.Item label="所属区域">{item?.areaName}</Descriptions.Item>
              <Descriptions.Item label={`资费(${detailData?.currencyName})`}>
                {item?.calcPrice}
              </Descriptions.Item>
              <Descriptions.Item label={`折后资费(${detailData?.currencyName})`}>
                {item?.discountPrice}
              </Descriptions.Item>
              <Descriptions.Item label={`干线费(${detailData?.currencyName})`}>
                {item?.moneyTrunk}
              </Descriptions.Item>
              <Descriptions.Item label={`附加费(${detailData?.currencyName})`}>
                {item?.acctachPrice}
              </Descriptions.Item>
              <Descriptions.Item label={`账单金额(${detailData?.currencyName})`}>
                {item?.arPrice}
              </Descriptions.Item>
            </Descriptions>
          ) : (
            <Descriptions>
              <Descriptions.Item label="制单账号">{item?.customerCode}</Descriptions.Item>
              <Descriptions.Item label="计费时间">
                {dayjs(item?.timeOfCalc).format('YYYY-MM-DD')}
              </Descriptions.Item>
              <Descriptions.Item label="转单号">{item?.exchangeNumber}</Descriptions.Item>
              <Descriptions.Item label="产品名称">{item?.productName}</Descriptions.Item>
              <Descriptions.Item label="国家">{item?.regionName}</Descriptions.Item>
              <Descriptions.Item label="邮编">{item?.postCode}</Descriptions.Item>
              <Descriptions.Item label="件数">{item?.piece}</Descriptions.Item>
              <Descriptions.Item label="实重(g)">{item?.weight}</Descriptions.Item>
              <Descriptions.Item
                label={
                  billWeightunit === ''
                    ? '体积重'
                    : billWeightunit === 'kg'
                    ? '体积重(kg)'
                    : '体积重(g)'
                }
              >
                {item?.dimWeight}
              </Descriptions.Item>
              <Descriptions.Item label="计费重量(克)">{item?.calcWeight}</Descriptions.Item>
              <Descriptions.Item label="费用名称">{item?.itemName}</Descriptions.Item>
              <Descriptions.Item label={`折前金额(${detailData?.currencyName})`}>
                {item?.arPrice}
              </Descriptions.Item>
              <Descriptions.Item label={`金额(${detailData?.currencyName})`}>
                {item?.discountPrice}
              </Descriptions.Item>
            </Descriptions>
          )
        // 应收国内快递退件
        case 'TC08':
        case 'BTC08':
        case 'CTC08':
          return detailData?.indicator != 2 ? (
            <Descriptions>
              <Descriptions.Item label="国内快递公司">{item?.supplierName?.[0]}</Descriptions.Item>
              <Descriptions.Item label="国内快递单号">{item?.domesticNo}</Descriptions.Item>
              <Descriptions.Item label="制单账号">{item?.accountCode?.[0]}</Descriptions.Item>
              <Descriptions.Item label="运单号">{item?.waybillNumber?.[0]}</Descriptions.Item>

              <Descriptions.Item label="账单日期">
                {dayjs(item?.timeOfExpress).format('YYYY-MM-DD')}
              </Descriptions.Item>
              <Descriptions.Item label="退件时间">
                {dayjs(item?.returnTime?.[0]).format('YYYY-MM-DD')}
              </Descriptions.Item>
              <Descriptions.Item label="产品名称">
                {item?.expressProductNames?.[0]}
              </Descriptions.Item>
              <Descriptions.Item label="目的地">{item?.regionDesc?.[0]}</Descriptions.Item>
              <Descriptions.Item label="退件原因">{item?.reasonDesc?.[0]}</Descriptions.Item>
              <Descriptions.Item label={`退件金额(${detailData?.currencyName})`}>
                {item?.MoneyToDif}
              </Descriptions.Item>
            </Descriptions>
          ) : (
            <Descriptions>
              <Descriptions.Item label="制单账号">{item?.customerCode}</Descriptions.Item>
              <Descriptions.Item label="计费时间">
                {dayjs(item?.timeOfCalc).format('YYYY-MM-DD')}
              </Descriptions.Item>
              <Descriptions.Item label="转单号">{item?.exchangeNumber}</Descriptions.Item>
              <Descriptions.Item label="产品名称">{item?.productName}</Descriptions.Item>
              <Descriptions.Item label="国家">{item?.regionName}</Descriptions.Item>
              <Descriptions.Item label="邮编">{item?.postCode}</Descriptions.Item>
              <Descriptions.Item label="件数">{item?.piece}</Descriptions.Item>
              <Descriptions.Item label="实重(g)">{item?.weight}</Descriptions.Item>
              <Descriptions.Item
                label={
                  billWeightunit === ''
                    ? '体积重'
                    : billWeightunit === 'kg'
                    ? '体积重(kg)'
                    : '体积重(g)'
                }
              >
                {item?.dimWeight}
              </Descriptions.Item>
              <Descriptions.Item label="计费重量(克)">{item?.calcWeight}</Descriptions.Item>
              <Descriptions.Item label="费用名称">{item?.itemName}</Descriptions.Item>
              <Descriptions.Item label={`折前金额(${detailData?.currencyName})`}>
                {item?.arPrice}
              </Descriptions.Item>
              <Descriptions.Item label={`金额(${detailData?.currencyName})`}>
                {item?.discountPrice}
              </Descriptions.Item>
            </Descriptions>
          )
        // 应收优惠
        case 'TC15':
        case 'BTC15':
        case 'CTC15':
          return detailData?.indicator != 2 ? (
            <Descriptions>
              <Descriptions.Item label="序号">{item?.rowNum}</Descriptions.Item>
              <Descriptions.Item label="订单号">{item?.orderNumber}</Descriptions.Item>
              <Descriptions.Item label="转单号">{item?.exchangeNumber}</Descriptions.Item>
              <Descriptions.Item label="参考号">{item?.yanwenNumber}</Descriptions.Item>
              <Descriptions.Item label="账单日期">
                {dayjs(item?.calcTime).format('YYYY-MM-DD')}
              </Descriptions.Item>
              <Descriptions.Item label="产品名称">{item?.productName}</Descriptions.Item>
              <Descriptions.Item label="所属区域">{item?.areaName}</Descriptions.Item>
              <Descriptions.Item label="目的地">{item?.regionName}</Descriptions.Item>
              <Descriptions.Item label="计费重量(克)">{item?.calcWeight}</Descriptions.Item>
              <Descriptions.Item label={`优惠返利(${detailData?.currencyName})`}>
                {item?.arPrice}
              </Descriptions.Item>
            </Descriptions>
          ) : (
            <Descriptions>
              <Descriptions.Item label="制单账号">{item?.customerCode}</Descriptions.Item>
              <Descriptions.Item label="计费时间">
                {dayjs(item?.timeOfCalc).format('YYYY-MM-DD')}
              </Descriptions.Item>
              <Descriptions.Item label="转单号">{item?.exchangeNumber}</Descriptions.Item>
              <Descriptions.Item label="产品名称">{item?.productName}</Descriptions.Item>
              <Descriptions.Item label="国家">{item?.regionName}</Descriptions.Item>
              <Descriptions.Item label="邮编">{item?.postCode}</Descriptions.Item>
              <Descriptions.Item label="件数">{item?.piece}</Descriptions.Item>
              <Descriptions.Item label="实重(g)">{item?.weight}</Descriptions.Item>
              <Descriptions.Item
                label={
                  billWeightunit === ''
                    ? '体积重'
                    : billWeightunit === 'kg'
                    ? '体积重(kg)'
                    : '体积重(g)'
                }
              >
                {item?.dimWeight}
              </Descriptions.Item>
              <Descriptions.Item label="计费重量(克)">{item?.calcWeight}</Descriptions.Item>
              <Descriptions.Item label="费用名称">{item?.itemName}</Descriptions.Item>
              <Descriptions.Item label={`折前金额(${detailData?.currencyName})`}>
                {item?.arPrice}
              </Descriptions.Item>
              <Descriptions.Item label={`金额(${detailData?.currencyName})`}>
                {item?.discountPrice}
              </Descriptions.Item>
            </Descriptions>
          )
        // 应收税费
        case 'BTC35':
        case 'CTC35':
          return detailData?.indicator != 2 ? (
            <Descriptions>
              <Descriptions.Item label="制单账号">{item?.customerCode}</Descriptions.Item>
              <Descriptions.Item label="账单日期">
                {dayjs(item?.calcTime).format('YYYY-MM-DD')}
              </Descriptions.Item>
              <Descriptions.Item label="订单号">{item?.orderNumber}</Descriptions.Item>
              <Descriptions.Item label="产品名称">{item?.productName}</Descriptions.Item>
              <Descriptions.Item label="国家">{item?.regionName}</Descriptions.Item>
              <Descriptions.Item label={`税费(${detailData?.currencyName})`}>
                {item?.arPrice}
              </Descriptions.Item>
            </Descriptions>
          ) : (
            <Descriptions>
              <Descriptions.Item label="制单账号">{item?.customerCode}</Descriptions.Item>
              <Descriptions.Item label="计费时间">
                {dayjs(item?.timeOfCalc).format('YYYY-MM-DD')}
              </Descriptions.Item>
              <Descriptions.Item label="账单日期">
                {dayjs(item?.calcTime).format('YYYY-MM-DD')}
              </Descriptions.Item>
              <Descriptions.Item label="运单号">{item?.waybillNumber}</Descriptions.Item>
              <Descriptions.Item label="转单号">{item?.exchangeNumber}</Descriptions.Item>
              <Descriptions.Item label="产品名称">{item?.productName}</Descriptions.Item>
              <Descriptions.Item label="国家">{item?.regionName}</Descriptions.Item>
              <Descriptions.Item label="邮编">{item?.postCode}</Descriptions.Item>
              <Descriptions.Item label="件数">{item?.piece}</Descriptions.Item>
              <Descriptions.Item label="实重(g)">{item?.weight}</Descriptions.Item>
              <Descriptions.Item
                label={
                  billWeightunit === ''
                    ? '体积重'
                    : billWeightunit === 'kg'
                    ? '体积重(kg)'
                    : '体积重(g)'
                }
              >
                {item?.dimWeight}
              </Descriptions.Item>
              <Descriptions.Item label="计费重量(克)">{item?.calcWeight}</Descriptions.Item>
              <Descriptions.Item label="费用名称">{item?.itemName}</Descriptions.Item>
              <Descriptions.Item label={`折前金额(${detailData?.currencyName})`}>
                {item?.arPrice}
              </Descriptions.Item>
              <Descriptions.Item label={`金额(${detailData?.currencyName})`}>
                {item?.discountPrice}
              </Descriptions.Item>
            </Descriptions>
          )
        // 境外重派
        case 'BTC39':
          return (
            <Descriptions>
              <Descriptions.Item label="制单账号">{item?.customerCode}</Descriptions.Item>
              <Descriptions.Item label="账单日期">
                {dayjs(item?.timeOfExpress).format('YYYY-MM-DD')}
              </Descriptions.Item>
              <Descriptions.Item label="运单号">{item?.waybillNumber}</Descriptions.Item>
              <Descriptions.Item label="订单号">{item?.orderNumber}</Descriptions.Item>
              <Descriptions.Item label="新转单号">{item?.exchangeNumber}</Descriptions.Item>
              <Descriptions.Item label="产品名称">{item?.productName}</Descriptions.Item>
              <Descriptions.Item label="目的地">{item?.regionName}</Descriptions.Item>
              <Descriptions.Item label="计费重量(克)">{item?.calcWeight}</Descriptions.Item>
              <Descriptions.Item label={`重派金额(${detailData?.currencyName})`}>
                {item?.arPrice}
              </Descriptions.Item>
            </Descriptions>
          )
        // 首公里揽收
        case 'BTC38':
        case 'TC38':
          return (
            <Descriptions>
              <Descriptions.Item label="制单账号">{item?.customerCode}</Descriptions.Item>
              <Descriptions.Item label="计费时间">
                {dayjs(item?.TimeOfCalc).format('YYYY-MM-DD')}
              </Descriptions.Item>
              <Descriptions.Item label="账单日期">
                {dayjs(item?.timeOfExpress).format('YYYY-MM-DD')}
              </Descriptions.Item>
              <Descriptions.Item label="计费单号">{item?.waybillNumber}</Descriptions.Item>
              <Descriptions.Item label="包数">{item?.PackageNum}</Descriptions.Item>
              <Descriptions.Item label="产品名称">{item?.ProductName}</Descriptions.Item>
              <Descriptions.Item label="重量(克)">{item?.CalcWeight}</Descriptions.Item>
              <Descriptions.Item label="发货仓">{item?.CompanyCodeOfFromName}</Descriptions.Item>
              <Descriptions.Item label={`账单金额(${detailData?.currencyName})`}>
                {item?.ArPrice}
              </Descriptions.Item>
            </Descriptions>
          )
        default:
          return <></>
      }
    }

    return (
      <View key={index} className="mt-[8px] bg-[#fff] rounded-md">
        <View className="p-[6px]" style={{ borderBottom: '1px solid #F6F4F4' }}>
          <View className="flex justify-between items-center p-[6px]">
            <View className="text-[14px] text-[#3D3D3D]">{item?.orderNumber}</View>
            <View className="text-[14px] text-[#4AAC0F]">{item?.customerCode}</View>
          </View>
        </View>
        <View className="px-[6px] py-[8px] text-[12px]">
          {handleTransTypeRender(detailData?.transType)}
        </View>
      </View>
    )
  }

  function reset() {
    setData([])
    setTotal(0)
    setCurrentPage(1)
  }

  return (
    <View className="bg-content p-[10px]">
      <View className="px-[15px] my-[8px]">
        <View className="bg-[#F3F7EC] py-[13px] px-[8px] rounded-[4px]">
          <View className="flex justify-around " style={{ borderBottom: '1px solid #E7E7E7' }}>
            <View className="text-[14px] text-[#666] flex p-[12px]">
              {/* <View>总计票数：</View> */}
              <View className="text-[#4AAC0F]">
                {detailData?.startFDate}~{detailData?.endTDate}
              </View>
            </View>
            <View className="w-[1px] h-auto bg-[#E7E7E7]" />
            <View className="text-[14px] text-[#666] flex p-[12px]">
              {/* <View>账单金额(元)：</View> */}
              <View className="text-[#4AAC0F]">{detailData?.transTypeName}</View>
            </View>
          </View>
          <View className="flex justify-around">
            <View className="text-[14px] text-[#666] flex p-[12px]">
              <View>总计票数：</View>
              <View className="text-[#4AAC0F]">{sumRow ?? total}</View>
            </View>
            <View className="w-[1px] h-auto bg-[#E7E7E7]" />
            <View className="text-[14px] text-[#666] flex p-[12px]">
              <View>账单金额({detailData?.currencyName})：</View>
              <View className="text-[#4AAC0F]">{detailData?.billAmount}</View>
            </View>
          </View>
        </View>
      </View>
      <FlatList
        style={{ height: `${height}px` }}
        itemData={data}
        total={total}
        itemRender={handleItemRender}
        onRefresh={async () => {
          await new Promise<any>((resolve) => {
            setTimeout(() => {
              setData([])
              if (currentPage === 1) {
                reset()
                fetchTypeBillDetail()
              } else {
                reset()
              }
              resolve('')
            }, 300)
          })
        }}
        onScrollToLower={async () => {
          if (data.length === total) return
          await new Promise<any>((resolve) => {
            setTimeout(() => {
              setCurrentPage(currentPage + 1)
              resolve('')
            }, 300)
          })
        }}
      />
    </View>
  )
}

export default inject('bill')(observer(Index))
