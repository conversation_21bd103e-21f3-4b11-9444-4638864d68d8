import { getHistoricalBillList, getHistoryListByType } from '@/api/bill'
import FlatList from '@/components/FlatList/FlatList'
import { Image, Text, View } from '@tarojs/components'
import { useRouter } from '@tarojs/taro'
import { useMount, useUpdateEffect, useDebounceEffect } from 'ahooks'
import { useState, useEffect, useRef } from 'react'
import './index.scss'
import { DateFormatType } from '@/components/MultipleDateTimePicker'
import MultipleDateTimePicker from '@/components/MultipleDateTimePicker'
import { AtIcon } from 'taro-ui'
import { startTime, endTime } from '../freightBillSearch/components/SearchComponent'
import Taro from '@tarojs/taro'
import Shaixuan from '@/static/image/icon-shaixuan.png'
import PageContainerForm, { FormRefInterface } from '@/components/PageContainerForm'
import SearchSelectPicker from '@/components/SearchSelectPicker'
import { costTypeList } from '@/utils/commonConstant'

const Index = (props) => {
  const { billPeriod, currencyName, bill, scrollHeight } = props
  const loadingRef = useRef(false)  
  const formRef = useRef<FormRefInterface>(null)
  const [data, setData] = useState<Bill.getHistoricalBillListResponse['rows']>([])
  const [total, setTotal] = useState<number>(0)
  const [currentPage, setCurrentPage] = useState<number>(1)
  const [showDate, setShowDate] = useState(false)
  const [dateTime, setDateTime] = useState<string[]>([startTime, endTime])
  const [typeSelect, setTypeSelect] = useState<boolean>(false)
  const [billType, setBillType] = useState<string[]>([])

  useEffect(() => {
    if (billPeriod) {
      const startRefDate = billPeriod?.split('_')?.[0] ?? dateTime?.[0]
      const endRefDate = billPeriod?.split('_')?.[1] ?? dateTime?.[1]
      setDateTime([startRefDate, endRefDate])
    }
  }, [billPeriod])

  useDebounceEffect(
    () => {
      if (!dateTime[0] || !dateTime[1]) return // 添加空值保护
      init()
    },
    [currentPage, dateTime.join(), billType.join(), typeSelect],
    { wait: 300 }
  )

  function reset() {
    if (data.length === 0 && currentPage === 1) return
    setData([])
    setCurrentPage(1)
  }

  async function init() {
    if (loadingRef.current) return
    loadingRef.current = true
    try {
      const startRefDate = dateTime?.[0]
      const endRefDate = dateTime?.[1]
      const params = {
        currentPage: currentPage,
        pageSize: 10,
        startRefDate,
        endRefDate,
        billType: typeSelect ? billType?.filter((t) => t != 'all')?.join(',') : undefined,
      }
      const response = typeSelect
        ? await getHistoryListByType(params)
        : await getHistoricalBillList(params)
      if (response.success) {
        if (response.data?.result) {
          setTotal(response.data.total)
          setData([...data, ...(response.data?.rows ?? [])])
        } else {
          setData([])
          setTotal(0)
        }
      }
    } catch (error) {
      console.log(error)
    } finally {
      loadingRef.current = false
    }
  }

  const handleDateTime = (dateTime: string[]) => {
    reset()
    setDateTime(dateTime)
    setBillType([])
    setTypeSelect(false)
  }

  function handleItemRender(item: Bill.Row, index: number) {
    const handleGoDetail = (item: Bill.Row) => {
      bill.setTypeBillDetailData({ ...item, currencyName })
      Taro.navigateTo({
        url: '/pages/bill/typeBillDetail/index',
      })
    }

    return (
      <View
        key={index}
        className="bill-content-body"
        onClick={() => {
          handleGoDetail(item)
        }}
      >
        <View className="bill-content-date-text">
          <View>
            {item?.startFDate}~{item.endTDate}
          </View>
          <View>{item?.transTypeName}</View>
        </View>
        <View className="bill-content-details">
          <View className="bill-content-details-text">
            <Text style={{ color: '#938F8F' }}>账单金额：</Text>¥ {item?.billAmount}
          </View>
          <View className="bill-content-details-text">
            <Text style={{ color: '#938F8F' }}>入账金额：</Text>¥ {item?.balAmount}
          </View>
          <View className="bill-content-details-text">
            <Text style={{ color: '#938F8F' }}>调整标记：</Text>
            {item?.isChange === '0' ? '否' : '是'}
          </View>
        </View>
      </View>
    )
  }

  const handleSubmit = (values: any) => {
    reset()
    setTypeSelect(true)
    setBillType(values.billType)
  }

  return (
    <View>
      <View className="p-4 flex items-center justify-between bg-white">
        <View className="flex items-center">
          <View className="bill-select-date-text" onClick={() => setShowDate(!showDate)}>
            账单周期：{dateTime[0]} ~ {dateTime[1]}
          </View>
          <AtIcon className="ml-2" value="chevron-down" size="8" color="#888888"></AtIcon>
        </View>
        <View
          className="flex"
          onClick={() => {
            formRef?.current?.open()
          }}
        >
          <Image className="w-[13px] h-[13px]" src={Shaixuan} />

          <View className="ml-1 text-[14px] text-[#8D8D8D]">筛选</View>
        </View>
      </View>
      <FlatList
        className="h-[65vh] bill-content-list"
        // style={{ height: `${scrollHeight}px` }}
        itemData={data}
        total={total}
        itemRender={handleItemRender}
        onRefresh={async () => {
          await new Promise<any>((resolve) => {
            setTimeout(() => {
              setData([])
              if (currentPage === 1) {
                reset()
                init()
              } else {
                reset()
              }
              resolve('')
            }, 300)
          })
        }}
        onScrollToLower={async () => {
          if (data.length === total) return
          await new Promise<any>((resolve) => {
            setTimeout(() => {
              setCurrentPage(currentPage + 1)
              resolve('')
            }, 300)
          })
        }}
      />
      <MultipleDateTimePicker
        dateFormat={DateFormatType.YYYY_MM_DD}
        value={dateTime}
        onChange={handleDateTime}
        isOpened={showDate}
        changeShowStatus={() => setShowDate(!showDate)}
      />
      <PageContainerForm
        formRef={formRef}
        initialValue={{
          billType: costTypeList?.map((t) => t.value),
        }}
        onSubmit={async (values) => {
          try {
            handleSubmit(values)
          } catch (error) {
            console.log(error)
          }
        }}
      >
        <PageContainerForm.Item multiple label="费用类型" name="billType" type="picker">
          <SearchSelectPicker multiple title="请选择费用类型" options={costTypeList} />
        </PageContainerForm.Item>
      </PageContainerForm>
    </View>
  )
}

export default Index
