import { Image, Text, View } from '@tarojs/components'
import { AtIcon } from 'taro-ui'
import './index.scss'
import { useState } from 'react'
import MultipleDateTimePicker, { DateFormatType } from '@/components/MultipleDateTimePicker'
import dayjs from 'dayjs'
import { useMount, useUpdateEffect } from 'ahooks'
import { getAccountPeriodList } from '@/api/bill'
import Empty from '@/components/Empty'
import Taro from '@tarojs/taro'
import { endTime } from '../freightBillSearch/components/SearchComponent'
import Shaixuan from '@/static/image/icon-shaixuan.png'

const startTime = dayjs().subtract(6, 'month').format('YYYY-MM-DD')

const Index = (props) => {
  const { onTabChange } = props
  const [showDate, setShowDate] = useState(false)
  const [dateTime, setDateTime] = useState<string[]>([startTime, endTime])
  const [data, setData] = useState<Bill.getAccountPeriodListResponse['list']>([])

  useMount(() => {
    init()
  })
  useUpdateEffect(() => {
    init()
  }, [dateTime])

  async function init() {
    try {
      const response = await getAccountPeriodList({
        startRefDate: dateTime[0],
        endRefDate: dateTime[1],
        chkStatus: 3,
      })
      if (response.success) {
        setData(response.data.list)
      }
    } catch (error) {
      console.log(error)
    }
  }

  const handleDateTime = (dateTime: string[]) => {
    setDateTime(dateTime)
  }

  function handleGotoTypeBill(item: Bill.List) {
    onTabChange('2', item.billPeriod)
    // Taro.navigateTo({
    //   url: `/pages/bill/typeList/index?billPeriod=${item.billPeriod}`,
    // })
  }

  return (
    <View className="bg-content">
      <View className="p-4 flex items-center justify-between bg-white">
        <View className="flex items-center">
          <View className="bill-select-date-text" onClick={() => setShowDate(!showDate)}>
            账单周期：{dateTime[0]} ~ {dateTime[1]}
          </View>
          <AtIcon className="ml-2" value="chevron-down" size="8" color="#888888"></AtIcon>
        </View>
        <View className="flex" onClick={() => setShowDate(!showDate)}>
          <Image className="w-[13px] h-[13px]" src={Shaixuan} />

          <View className="ml-1 text-[14px] text-[#8D8D8D]">筛选</View>
        </View>
      </View>
      {(!data || data?.length === 0) && (
        <Empty
          className="mt-10"
          description={
            <View className="flex flex-col items-center">
              <View className="text-[14px] text-[#999999] mb-[8px]">
                页面是空的，你可以尝试其它操作！
              </View>
              <View className="text-[18px] text-[#80C555]">暂无数据</View>
            </View>
          }
        />
      )}
      <View className="bill-content-list">
        {data &&
          data.map((item, index) => (
            <View
              key={index}
              className="bill-content-body"
              onClick={() => {
                handleGotoTypeBill(item)
              }}
            >
              <View className="bill-content-date-text">
                <View>{item?.billPeriod}</View>
                <View style={{ color: item?.chkStatus === 2 ? '#E85B5B' : '#4AAC0F' }}>
                  {item?.chkStatus == 1 ? '已清' : '未清'}
                </View>
              </View>
              <View className="bill-content-details">
                <View className="bill-content-details-text">
                  <Text style={{ color: '#938F8F' }}>账单金额：</Text>¥ {item?.billAmount}
                </View>
                <View className="bill-content-details-text">
                  <Text style={{ color: '#938F8F' }}>入账金额：</Text>¥ {item?.balAmount}
                </View>
                <View className="bill-content-details-text">
                  <Text style={{ color: '#938F8F' }}>未清金额：</Text>¥ {item?.balingAmount}
                </View>
                <View className="bill-content-details-text">
                  <Text style={{ color: '#938F8F' }}>到期日：</Text>
                  {item?.dueDate}
                </View>
              </View>
            </View>
          ))}
      </View>
      <MultipleDateTimePicker
        dateFormat={DateFormatType.YYYY_MM_DD}
        value={dateTime}
        onChange={handleDateTime}
        isOpened={showDate}
        changeShowStatus={() => setShowDate(!showDate)}
      />
    </View>
  )
}

export default Index
