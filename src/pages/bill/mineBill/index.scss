.icon {
  width: 15px;
  height: 15px;
  margin-left: 10px;
}

.section {
  margin-left: 9px;
  font-size: 14px;
  font-weight: 400;
  color: rgb(204, 204, 204);
  width: 100%;
  text-align: left;
}
.cnt_row {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  background-color: rgb(243, 243, 243);
  border-radius: 50px;
  margin-left: 20px;
  margin-right: 15px;
  margin-bottom: 15px;
  margin-top: 15px;
  height: 30px;
}
.mod {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
  background-color: #fff;
  margin-bottom: 8px;
}

// search.scss end

.sps_txt1 {
  flex-shrink: 0;
  overflow: hidden;
  font-size: 16px;
  line-height: 1.38;
  font-weight: bold;
  margin-right: 1.5px;
}
.sps_txt2 {
  flex-shrink: 0;
  font-size: 12px;
  line-height: 1.83;
}

.sps_img {
  width: 1px;
  height: 45px;
  margin-top: 5px;
  margin-right: 34px;
}

.sps_img1 {
  width: 1px;
  height: 45px;
  margin-top: 5px;
  margin-right: 37px;
}
.sps_icon {
  flex-shrink: 0;
  align-self: flex-end;
  width: 16px;
  height: 12px;
}
.index_sps_icon {
  flex-shrink: 0;
  align-self: flex-end;
  width: 16px;
  height: 16px;
}
.sps_txt3 {
  flex-shrink: 0;
  width: 50px;
  font-size: 12px;
  line-height: 1.42;
  color: rgb(150, 150, 150);
  text-align: left;
}
.sps_cnt_row2 {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 71px;
  height: 12.5px;
}
.sps_cnt_row3 {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  align-self: center;
  //width: 45px;
  height: 22px;
  margin-top: 8px;
  margin-left: 4px;
  color: rgb(97, 97, 97);
}
.sps_cnt_col2 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 71px;
  height: 42.5px;
  margin-top: 10px;
  margin-right: 23px;
}
.sps_cnt_row {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  width: 375px;
  height: 88px;
  background-color: rgb(255, 255, 255);
}
.sps_cnt_row_text {
  width: 375px;
  display: flex;
  flex-direction: row-reverse;
  background-color: rgb(255, 255, 255);
  padding-bottom: 10px;
}
.sps_cnt_row_text_content {
  color: #ada9a9;
  font-size: 10px;
  margin-right: 13px;
  font-weight: 400;
  line-height: 14px;
}
.sps_mod {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 375px;
  height: 88px;
  font-weight: 400;
  margin-bottom: 11px;
}

// smallPacketStatus.scss end

.cb_check_bill {
  width: 50px;
  height: 14px;
  color: #555555;
  font-size: 12px;
  font-weight: 400;
  line-height: 14px;
}
.cb_txt {
  flex-shrink: 0;
  align-self: flex-start;
  width: 169px;
  margin-top: 8px;
  margin-left: 12px;
  font-size: 14px;
  line-height: 1.43;
  text-align: left;
}
.cb_yuan {
  flex-shrink: 0;
  overflow: hidden;
  font-size: 12px;
  line-height: 2;
}
.cb_price {
  flex-shrink: 0;
  font-size: 20px;
  line-height: 1.43;
  font-weight: bold;
}
.cb_price_wrap {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: baseline;
  align-self: flex-start;
  width: 340px;
  height: 20px;
  margin-top: 6px;
  margin-left: 13px;
}
.cb_cnt_col {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  align-self: flex-start;
  width: 355px;
  height: 73px;
  background-image: linear-gradient(180deg, rgba(83, 223, 0, 0.11) 0%, rgba(140, 200, 61, 0) 100%);
  border-radius: 8px;
}
.cb_wrapper {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
  width: 355px;
  height: 79px;
  background-color: rgb(255, 255, 255);
  border-radius: 8px;
  border-bottom-left-radius: 0px;
  border-bottom-right-radius: 0px;
}
.cb_mod {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
  width: 355px;
  height: 79px;
  font-weight: 400;
  color: rgb(0, 0, 0);
}

.cb_content_txt1 {
  flex-shrink: 0;
  overflow: hidden;
  font-size: 14px;
  line-height: 1.38;
  font-weight: bold;
}
.cb_content_txt2 {
  flex-shrink: 0;
  margin-right: 1.5400009155273438px;
  font-size: 12px;
  line-height: 1.83;
}

.cb_content_img {
  width: 1px;
  height: 30px;
  margin-top: 5px;
  margin-right: 30px;
}

.cb_content_img1 {
  width: 1px;
  height: 30px;
  margin-top: 5px;
  margin-right: 30px;
  margin-left: 10px;
}
.cb_content_icon {
  flex-shrink: 0;
  align-self: flex-end;
  width: 16px;
  height: 12px;
}
.cb_content_txt3 {
  color: #9b9b9b;
  font-size: 12px;
  font-weight: 400;
  line-height: 17px;
}
.cb_content_cnt_row2 {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 90px;
  height: 17px;
}
.cb_content_cnt_row3 {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: baseline;
  align-self: flex-start;
  width: 45px;
  height: 22px;
  margin-top: 8px;
  margin-left: 4px;
  color: rgb(97, 97, 97);
}
.cb_content_cnt_col2 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 90px;
  height: 42.5px;
  margin-top: 1px;
}
.cb_content_cnt_col3{
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 150px;
  height: 42.5px;
  margin-top: 1px;
}
.cb_content_cnt_row {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  width: 355px;
  height: 62px;
  background-color: rgb(255, 255, 255);
}

// customerBill.scss end

.na_instruction {
  flex-shrink: 0;
  // width: 47px;
  margin-left: 10px;
  font-size: 12px;
  line-height: 1.42;
  color: rgba(83, 209, 8, 1);
  text-align: left;
  font-weight: bold; /* 设置字体加粗 */
  font-style: italic; /* 设置字体为斜体 */
}
.na_img {
  width: 1px;
  height: 18px;
  margin-left: 8px;
}
.na_line1 {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.na_txt {
  flex-shrink: 0;
  width: 206px;
  margin-left: 13px;
  font-size: 12px;
  line-height: 1.42;
  color: rgb(111, 106, 106);
  text-align: left;
}
.na_txt1 {
  flex-shrink: 0;
  width: 9px;
  margin-top: -3px;
  margin-left: 46px;
  font-size: 12px;
  line-height: 1.42;
  color: rgb(225, 216, 216);
  text-align: left;
}
.na_cnt_row {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  width: 355px;
  height: 36px;
  background-color: rgb(255, 255, 255);
  border-radius: 6px;
}
.na_mod {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
  width: 355px;
  height: 36px;
  font-weight: 400;
  margin-bottom: 10px;
  --nutui-noticebar-background: rgb(255, 255, 255);
}

.qe_mod {
  padding-top: 10px;
  padding-bottom: 10px;
  width: 355px;
  background-color: #fff;
  border-radius: 6px;
}

.qe_img {
  width: 38px;
  height: 38px;
}

.qe_text {
  color: #3d3d3d;
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
}

.notice_custom {
  .at-noticebar {
    background: #fff;
    padding: 0;
    width: 100%;
    .at-noticebar__content-text {
      font-size: 12px;
      line-height: 3;
    }
  }
}
