import { useRef, useState } from 'react'
import { Text, View } from '@tarojs/components'
import CustomerBill from './components/CustomerBill'
import ScrollTabs from '@/components/ScrollTabs'
import FreightBillSearch from '../freightBillSearch'
import AccountPeriodList from '../accountPeriodList'
import TypeBillList from '../typeList'
import { inject, observer } from 'mobx-react'
import { windowHeight } from '@/utils'

const Index = (props) => {
  const scrollHeight = windowHeight - 340
  const [tabValue, setTabValue] = useState('0')
  const [billPeriod, setBillPeriod] = useState()
  const [currencyName, setCurrencyName] = useState()

  const tabList = [
    {
      title: '运单明细',
      value: '0',
    },
    {
      title: '账期清单',
      value: '1',
    },
    {
      title: '类型清单',
      value: '2',
    },
  ]
  function onTabChange(value: string): void {
    setTabValue(value)
    if (value !== '2') {
      setBillPeriod(undefined)
    }
  }

  function handleTabChange(value: string, params: any): void {
    onTabChange(value)
    if (value === '2') {
      setBillPeriod(params)
    }
  }

  return (
    <View className="bg-content p-[10px]">
      <CustomerBill setCurrencyName={setCurrencyName} />
      <ScrollTabs tabList={tabList} tabValue={tabValue} onTabChange={onTabChange} />
      {tabValue === '0' && <FreightBillSearch {...props} scrollHeight={scrollHeight} />}
      {tabValue === '1' && <AccountPeriodList {...props} onTabChange={handleTabChange} />}
      {tabValue === '2' && (
        <TypeBillList {...props} currencyName={currencyName} billPeriod={billPeriod} scrollHeight={scrollHeight} />
      )}
    </View>
  )
}

export default inject('bill')(observer(Index))
