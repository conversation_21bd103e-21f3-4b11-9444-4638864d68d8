import { View } from '@tarojs/components'
import { windowHeight } from '@/utils'
import Descriptions from '@/components/Descriptions'
import { inject, observer } from 'mobx-react'
import { Bill } from '@/store/modules/bill'

const Index = ({ bill }: { bill: Bill }) => {
  const { detailData } = bill

  return (
    <View className={`bg-content p-[10px] `}>
      <View className="mt-[8px] bg-[#fff] rounded-md">
        <View className="p-[6px]" style={{ borderBottom: '1px solid #F6F4F4' }}>
          <View className="flex justify-between items-center p-[6px]">
            <View className="text-[14px] text-[#3D3D3D]">{detailData?.orderNumber}</View>
            <View className="text-[14px] text-[#4AAC0F]">{detailData?.customerCode}</View>
          </View>
        </View>
        <View className="px-[6px] py-[8px] text-[12px]">
          <Descriptions>
            <Descriptions.Item label="订单号">{detailData?.orderNumber}</Descriptions.Item>
            <Descriptions.Item label="产品名称">{detailData?.productName}</Descriptions.Item>
            <Descriptions.Item label="目的地">{detailData?.regionName}</Descriptions.Item>
            <Descriptions.Item label="长 X 宽 X 高(CM)">
              {detailData?.expressLength} X {detailData?.expressWidth} X {detailData?.expressHeight}
            </Descriptions.Item>
          </Descriptions>
          <Descriptions column={2}>
            <Descriptions.Item label="实际重量(G)">{detailData?.weight}</Descriptions.Item>
            <Descriptions.Item label="计费重量(G)">{detailData?.calcWeight}</Descriptions.Item>
            <Descriptions.Item label="账单总金额（元）">{detailData?.money}</Descriptions.Item>
          </Descriptions>
          <View className="px-[15px] my-[8px]">
            <View className="bg-[#F3F7EC] py-[13px] px-[8px] rounded-[4px]">
              <View className="flex justify-around " style={{ borderBottom: '1px solid #E7E7E7' }}>
                <View className="text-[14px] text-[#666] flex p-[12px]">
                  <View>费用类型：</View>
                  <View className="text-[#4AAC0F]">{detailData?.transTypeName}</View>
                </View>
                <View className="w-[1px] h-auto bg-[#E7E7E7]" />
                <View className="text-[14px] text-[#666] flex p-[12px]">
                  <View>账单金额(元)：</View>
                  <View className="text-[#4AAC0F]">{detailData?.arPrice}</View>
                </View>
              </View>
              {/* <View className="flex justify-around">
                <View className="text-[14px] text-[#666] flex p-[12px]">
                  <View>费用类型：</View>
                  <View className='text-[#4AAC0F]'>应收快件</View>
                </View>
                <View className="w-[1px] h-auto bg-[#E7E7E7]" />
                <View className="text-[14px] text-[#666] flex p-[12px]">
                  <View>账单金额(元)：</View>
                  <View className='text-[#4AAC0F]'>30</View>
                </View>
              </View> */}
            </View>
          </View>
          <Descriptions>
            <Descriptions.Item label="账单日期">{detailData?.dateOfBill}</Descriptions.Item>
            <Descriptions.Item label="计费时间">{detailData?.timeOfCalc}</Descriptions.Item>
          </Descriptions>
        </View>
      </View>
    </View>
  )
}

export default inject('bill')(observer(Index))
