import Stores from '@/types/stores'
import { ScrollView, Text, View } from '@tarojs/components'
import { inject, observer } from 'mobx-react'
import { useMount } from 'ahooks'
import { useState } from 'react'
import './index.scss'
import RichTextView from '@/components/RichTextView'

const Index = ({ messageNotification }) => {
  const { detailInfo } = messageNotification

  return (
    <View className="py-[13px] px-[12px] bg-content" catchMove>
      <ScrollView scrollY style={{ height: 'calc(100vh - 70px)', backgroundColor: '#fff' }}>
        <View className="p-[6px]" style={{ borderBottom: '1px solid #F6F4F4' }}>
          <View className="flex justify-between items-center p-[6px]">
            <View className="text-[14px] text-[#4AAC0F] flex">{detailInfo?.subscribeType}</View>
          </View>
        </View>
        <View className="text-[14px] text-[#3D3D3D] p-[12px] leading-[22px]">
          <RichTextView content={detailInfo?.pushText} />
        </View>

        {/* <ApplyInfo detailInfo={detailInfo} />
        <TimeInfo detailInfo={detailInfo} />
        <MaterialInfo detailInfo={detailInfo} /> */}
      </ScrollView>
    </View>
  )
}

export default inject('messageNotification')(observer(Index))
