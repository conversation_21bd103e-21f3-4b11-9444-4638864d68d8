import { Button, Text, View } from '@tarojs/components'
import { AtIcon, AtModal, AtModalAction, AtModalContent } from 'taro-ui'
import FlatList from '@/components/FlatList/FlatList'
import { useMemo, useState, useCallback } from 'react'
import RichTextView from '@/components/RichTextView'
import { useMount } from 'ahooks'
import { list } from '@/api/messageNotification'
import messageNotification from '@/store/modules/messageNotification'
import Taro from '@tarojs/taro'
import { observer, inject } from 'mobx-react'
import { read } from '@/api/messageNotification'
import './index.scss'
import { useUpdateEffect } from 'ahooks'

const tabList = [
  {
    key: 'all',
    value: '全部',
  },
  {
    key: '0',
    value: '未读',
  },
  {
    key: '1',
    value: '已读',
  },
]

const Index = () => {
  const [pageIndex, setPageIndex] = useState(1)
  const [total, setTotal] = useState(0)
  const [data, setData] = useState<Array<MessageNotification.NotificationListItem>>([])
  const [readSignClick, setReadSignClick] = useState<boolean>(true)
  const [tabValue, setTabValue] = useState<string>('all')
  const [isOpened, setIsOpened] = useState<boolean>(false)
  useMount(() => {
    init()
  })

  useUpdateEffect(() => {
    init(tabValue === 'all' ? undefined : tabValue)
  }, [tabValue])

  const init = async (readSign: string | undefined = undefined) => {
    try {
      const response = await list({
        current: pageIndex,
        size: 10,
        readSign,
      })
      if (response.success) {
        setData(response.data.data)
        setTotal(response.data.total)
      }
    } catch (error) {
      console.log(error)
    }
  }

  function handleItemRender(item: MessageNotification.NotificationListItem, index: number) {
    return (
      <View
        className="mx-[8px] my-[16px] bg-[#fff] rounded-md"
        key={index}
        onClick={async () => {
          try {
            if (!item.readSign) {
              await read([
                {
                  id: item.id + '',
                },
              ])
            }
            messageNotification.setDetailInfo(item)
            Taro.navigateTo({
              url: '/pages/messageNotification/detail/index',
            })
          } catch (error) {
            console.log(error)
          }
        }}
      >
        <View className="p-[6px]" style={{ borderBottom: '1px solid #F6F4F4' }}>
          <View className="flex justify-between items-center p-[6px]">
            <View className="text-[14px] text-[#4AAC0F] flex">{item?.subscribeType}</View>
          </View>
        </View>
        <View className="p-[6px]" style={{ borderBottom: '1px solid #F6F4F4' }}>
          <View className="text-[14px] text-[#3D3D3D] p-[6px] leading-[22px]">
            <RichTextView content={item?.pushText} />
          </View>
        </View>
        <View className="p-[6px]">
          <View className="text-[12px] p-[6px] text-[#999] flex flex-row items-center justify-between">
            <View>{item?.createTime}</View>
            <View className={`${item?.readSign ? 'text-[#999]' : 'text-[#ff0000]'}`}>
              {item?.readSign ? '已读' : '未读'}
            </View>
          </View>
        </View>
      </View>
    )
  }

  function reset() {
    setData([])
    setTotal(0)
    setPageIndex(1)
  }

  return (
    <View className="bg-content" catchMove>
      <View className="flex justify-between items-center w-full bg-[#fff] text-[12px]">
        <View className="flex flex-row items-center gap-[8px]">
          {tabList.map((item) => (
            <View
              key={item.key}
              onClick={() => {
                setTabValue(item.key)
              }}
              className={`${tabValue === item.key ? 'tab-select' : 'tab-select-normal'}`}
            >
              {item.value}
            </View>
          ))}
        </View>
        <View
          className="px-[20px]  flex flex-row items-center gap-[8px]"
          onClick={async () => {
            if (data.length === 0) return Taro.showToast({ title: '暂无数据', icon: 'none' })
            setIsOpened(true)
          }}
        >
          <View>全部已读</View>
          <AtIcon value="check-circle" size="8" color="#999" />
        </View>
      </View>

      {useMemo(
        () => (
          <FlatList
            className="h-[85vh]"
            itemData={data}
            total={total}
            itemRender={handleItemRender}
            onRefresh={async () => {
              await new Promise<any>((resolve) => {
                setTimeout(() => {
                  setData([])
                  if (pageIndex === 1) {
                    reset()
                    init(tabValue === 'all' ? undefined : tabValue)
                  } else {
                    reset()
                  }
                  resolve('')
                }, 300)
              })
            }}
            onScrollToLower={async () => {
              if (data.length === total) return
              await new Promise<any>((resolve) => {
                setTimeout(() => {
                  setPageIndex(pageIndex + 1)
                  resolve('')
                }, 300)
              })
            }}
          />
        ),
        [data, total, pageIndex]
      )}
      <AtModal isOpened={isOpened} onClose={() => setIsOpened(false)}>
        <View className="text-center rounded-[5px]">
          <AtModalContent>
            <View className="at-col at-col-12 flex flex-col text-[14px] text-[#3D3D3D] p-[6px] leading-[22px]">
              是否全部已读
            </View>
          </AtModalContent>
          <AtModalAction>
            <Button onClick={() => setIsOpened(false)}>取消</Button>
            <Button
              onClick={async () => {
                setIsOpened(false)
                await read(
                  data?.filter((item) => !item.readSign).map((item) => ({ id: item.id + '' }))
                )
                setPageIndex(1)
                reset()
                init()
              }}
            >
              确定
            </Button>
          </AtModalAction>
        </View>
      </AtModal>
    </View>
  )
}

export default inject('messageNotification')(observer(Index))
