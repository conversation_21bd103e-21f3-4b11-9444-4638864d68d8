import { useRef, useState } from 'react'
import { Button, Input, Radio, RadioGroup, Textarea, View } from '@tarojs/components'
import Form, { FormRefInterface } from '@/components/Form'
import SearchSelectPicker from '@/components/SearchSelectPicker'
import MultipleDateTimePicker, { DateFormatType } from '@/components/MultipleDateTimePicker'
import Taro from '@tarojs/taro'
import dayjs from 'dayjs'
import { getPacketWarehouse } from '@/api/common'
import {
  materialTypeQuery,
  materialCollectPoint,
  materialCreate,
  materialDetail,
  materialUpdate,
} from '@/api/materialApplication'
import { useMount, useUpdateEffect, useUnmount } from 'ahooks'
import { observer, inject } from 'mobx-react'
import Stores from '@/types/stores'
import { AtIcon } from 'taro-ui'

const Index = ({ materialApplication }) => {
  const formRef = useRef<FormRefInterface>(null)
  const [applyCount, setApplyCount] = useState('')
  const [detail, setDetail] = useState<any[]>([])
  const [materialList, setMaterialList] = useState<MaterialApplication.MaterialTypeQueryResponse[]>(
    []
  )
  const [packetWarehouseList, setPacketWarehouseList] = useState<any[]>([])
  const [materialCollectPointList, setMaterialCollectPointList] = useState<
    MaterialApplication.MaterialCollectPointResponse[]
  >([])
  const [sendType, setSendType] = useState()
  const [detailParamsInfo, setDetailParamsInfo] = useState<any>({})

  useMount(() => {
    init()
  })

  useUnmount(() => {
    formRef.current?.resetFields()
    materialApplication.setDetailInfoParams({})
    setDetail([])
    setApplyCount('')
    setMaterialList([])
    setMaterialCollectPointList([])
    setPacketWarehouseList([])
    setSendType(undefined)
    setDetailParamsInfo({})
  })

  function init() {
    setDetailParamsInfo({ ...materialApplication.detailInfoParams })
    fetchPacketWarehouse()
  }

  useUpdateEffect(() => {
    if (detailParamsInfo?.warehouse) {
      handleUpdateInit()
    }
  }, [detailParamsInfo])

  async function handleUpdateInit() {
    const { materialTypeListPromiseData = [] } =
      (await handleWarehouseChange(detailParamsInfo.warehouse)) ?? {}
    const detailData = detailParamsInfo.materielList?.map((item) => {
      const material = materialTypeListPromiseData.find(
        (val) => val.materielTypeName === item.materiel_type_name
      )
      return {
        ...material,
        materielTypeName: material?.materielTypeName,
        applyNumber: item.apply_piece,
      }
    })
    setDetail(detailData)
    setApplyCount(detailData[0]?.applyNumber)
    const response = await materialDetail({
      customerCode: detailParamsInfo.customer_code,
      orderId: detailParamsInfo.order_id,
    })
    if (response.success) {
      setSendType(response?.data?.sendType?.toString())
      formRef.current?.setFieldsValue({
        materielTypeId: detailData[0]?.materielTypeId,
        collectingPointIndex: 0,
        sendType: response?.data?.sendType?.toString(),
        // materialUseTime: [
        //   dayjs(detailParamsInfo.useDate).format('YYYY-MM-DD'),
        //   dayjs(detailParamsInfo.expireDate).format('YYYY-MM-DD'),
        // ],
        notes: detailParamsInfo.notes,
        warehouseCode: detailParamsInfo.warehouse,
      })
    }
  }

  async function fetchPacketWarehouse() {
    try {
      const response = await getPacketWarehouse()
      if (response.success) {
        setPacketWarehouseList(response.data)
      }
    } catch (error) {
      console.log(error)
    }
  }

  async function handleSaveDetail() {
    try {
      const { materielTypeId } = await formRef.current?.validateFieldsValue(['materielTypeId'])
      const material = materialList.find((item: any) => item.materielTypeId === materielTypeId)

      // 检查是否输入数量
      if (applyCount === '') {
        Taro.showToast({
          title: '请输入申请数量',
          icon: 'none',
        })
        return
      }

      // 检查数量是否为正数且不超过6位
      const count = parseInt(applyCount)
      if (count <= 0) {
        Taro.showToast({
          title: '请输入正确物料种类数量',
          icon: 'none',
        })
        return
      }
      if (applyCount.length > 6) {
        Taro.showToast({
          title: '请输入种类数量小于等于6位数',
          icon: 'none',
        })
        return
      }

      // 检查是否重复添加同一物料
      const isDuplicate = detail.some((item) => item.materielTypeId === materielTypeId)
      if (isDuplicate) {
        Taro.showToast({
          title: '同一物料种类不能重复添加',
          icon: 'none',
        })
        return
      }

      setDetail([
        ...detail,
        {
          materielTypeId,
          materielTypeName: material?.materielTypeName,
          applyNumber: count,
        },
      ])
      // 清空输入框
      setApplyCount('')
      // 添加成功提示
      Taro.showToast({
        title: '保存成功',
        icon: 'success',
      })
    } catch (error) {
      console.log(error)
    }
  }

  const validateUseDate = (values: any): string => {
    const lastDay = dayjs().add(30, 'day')
    if (dayjs(values[0]).isAfter(lastDay)) {
      return '最长只能申请近30天以内的物料'
    }
    if (dayjs(values[1]).isAfter(lastDay)) {
      return '最长只能申请近30天以内的物料'
    }
    return ''
  }

  async function handleWarehouseChange(value: string) {
    try {
      const materialTypeListPromiseData = (await fetchMaterialTypeQuery(value)) ?? []
      const materialCollectPointListPromiseData = (await fetchMaterialCollectPoint(value)) ?? []
      if (!detailParamsInfo?.warehouse) {
        formRef.current?.setFieldsValue({
          collectingPointIndex: undefined,
        })
      }
      return { materialTypeListPromiseData, materialCollectPointListPromiseData }
    } catch (error) {
      console.log(error)
    }
  }

  async function fetchMaterialTypeQuery(values: string) {
    try {
      const response = await materialTypeQuery({ warehouseCode: values })
      if (response.success) {
        setMaterialList(response.data)
      }
      return response?.data ?? []
    } catch (error) {
      console.log(error)
    }
  }

  async function fetchMaterialCollectPoint(values: string) {
    try {
      const response = await materialCollectPoint({ warehouseCode: values })
      if (response.success) {
        setMaterialCollectPointList(response.data)
        return response.data
      } else {
        setMaterialCollectPointList([])
        return []
      }
    } catch (error) {
      console.log(error)
    }
  }

  async function handleSubmit() {
    try {
      const values = await formRef.current?.validateFieldsValue()
      if (detail === undefined || detail.length <= 0) {
        Taro.showToast({
          title: '请保存物料明细',
          icon: 'none',
        })
        return
      }
      const dateTips = validateUseDate(values.materialUseTime)
      if (dateTips !== '') {
        Taro.showToast({
          title: dateTips,
          icon: 'none',
        })
        return
      }
      const collectingPoint = materialCollectPointList[values.collectingPointIndex]
      if (sendType === '0' && collectingPoint === undefined) {
        Taro.showToast({
          title: '请先创建揽收点',
          icon: 'none',
        })
        return
      }

      if ("useDate" in detailParamsInfo) {
        delete detailParamsInfo.useDate;
      }
      if ("expireDate" in detailParamsInfo) {
        delete detailParamsInfo.expireDate;
      }

      if(values?.notes?.length > 255) {
        Taro.showToast({
          title: '添加失败，失败原因：备注过长，最多可输入255个字',
          icon: 'none',
        })
        return
      }

      const params = {
        typeId: 1,
        opeSystemId: 0,
        sourceType: 0,
        ...detailParamsInfo,
        orderId: detailParamsInfo?.order_id,
        notes: values?.notes,
        data: {
          ...values,
          ...collectingPoint,
          detail: detail,
          useDate: dayjs(values.materialUseTime[0]).format('YYYY-MM-DD 00:00:00'),
          expireDate: dayjs(values.materialUseTime[1]).format('YYYY-MM-DD 23:59:59'),
        },
      }
      let response: any = null
      try {
        response = await materialCreate(params)
        if (response.success) {
          Taro.showToast({
            title: response.message,
            icon: 'success',
          })
          Taro.reLaunch({
            url: '/pages/materialApplication/list/index',
          })
        }
      } catch (error) {
        console.log(error)
      }
    } catch (error) {
      console.log(error)
    }
  }

  function handleDeleteDetail(index: number): void {
    const newDetail = detail.filter((_, idx) => idx !== index)
    setDetail(newDetail)
  }

  const disabledDate = (current: string) => ({
    disabledYear: Number(current) < dayjs().year(),
    disabledMonth: Number(current) < dayjs().month() + 1,
    disabledDay: Number(current) < dayjs().add(1, 'day').date(),
  })

  return (
    <View className="bg-content bg-[#fff] p-[10px] pt-[20px]">
      <Form formRef={formRef}>
        <Form.Item
          label="请选择交货仓"
          name="warehouseCode"
          direction="column"
          rules={[{ required: true, message: '请选择交货仓' }]}
        >
          <SearchSelectPicker
            title="请选择交货仓"
            options={packetWarehouseList.map((val) => ({
              ...val,
              label: `${val?.name}`,
              value: val.code,
            }))}
            onChange={(value: string) => {
              handleWarehouseChange(value)
            }}
          />
        </Form.Item>
        <Form.Item
          label="请选择领取方式"
          name="sendType"
          direction="column"
          rules={[{ required: true, message: '请选择领取方式' }]}
        >
          <RadioGroup
            className="mt-[10px] pb-[10px]"
            style={{ borderBottom: '1px solid #DCDCDC' }}
            onChange={(value: any) => {
              setSendType(value)
            }}
          >
            <Radio value="0" className="mr-[10px]" checked={sendType === '0'}>
              司机配送
            </Radio>
            <Radio value="1" checked={sendType === '1'}>
              自取
            </Radio>
          </RadioGroup>
        </Form.Item>
        {
          (sendType==undefined||sendType=='0')&&(
            <Form.Item
              label={
                <View className="flex flex-col gap-[10px]">
                  请选择揽收地址
                  <View className="text-[12px] text-[#ff0000]">
                    温馨提示：若没有对应揽收地址，请咨询客服。
                  </View>
                </View>
              }
              name="collectingPointIndex"
              direction="column"
              rules={[{ required: sendType === '0', message: '请选择揽收地址' }]}
            >
              <SearchSelectPicker
                title="请选择揽收地址"
                options={materialCollectPointList.map((val, index) => ({
                  ...val,
                  label: `${val?.provinceName}${val?.cityName}${val?.areaName}${val?.address}`,
                  value: index,
                }))}
              />
            </Form.Item>
          )
        }

        <Form.Item
          label="物料使用时间"
          name="materialUseTime"
          direction="column"
          rules={[{ required: true, message: '请选择物料使用时间' }]}
        >
          <MultipleDateTimePicker
            disabledDate={disabledDate}
            dateFormat={DateFormatType.YYYY_MM_DD}
          />
        </Form.Item>
        <Form.Item
          label="请选择物料种类"
          name="materielTypeId"
          direction="column"
          rules={[{ required: true, message: '请选择物料种类' }]}
        >
          <SearchSelectPicker
            title="请选择物料种类"
            options={materialList.map((val) => ({
              ...val,
              label: `${val?.materielTypeName}`,
              value: val.materielTypeId,
            }))}
          />
        </Form.Item>
        <View className={`flex items-center mb-[10px]`}>
          <View className={` pl-[5px] flex-1`}>
            <View className={`flex flex-col`}>
              <View className={`text-[14px] flex-1 required-label`}>请输入申请数量</View>
              <View className="flex gap-[8px] justify-between items-center">
                <Input
                  placeholder="请输入申请数量"
                  type="number"
                  className="py-[10px] pl-[10px] mt-[10px] flex-2 "
                  style={{ border: '1px solid #DCDCDC', borderRadius: '8px' }}
                  value={applyCount}
                  onInput={(e) => {
                    setApplyCount(e.detail.value)
                  }}
                />
                <Button
                  type="primary"
                  className="mt-[10px] mx-0 flex items-center h-[25px] text-[14px]"
                  onClick={handleSaveDetail}
                >
                  保存明细
                </Button>
              </View>
            </View>
          </View>
        </View>
        {detail?.length > 0 && (
          <View className={`flex items-center mb-[10px]`}>
            <View className={`pl-[5px] flex-1`}>
              {detail &&
                detail.map((item, index) => (
                  <View key={index} className="flex justify-between items-center">
                    <View className="flex items-center gap-[10px]">
                      <View className="text-[#3D3D3D]">{item.materielTypeName}</View>X
                      <View className="text-[#3D3D3D]">{item.applyNumber}</View>
                    </View>
                    <AtIcon
                      value="trash"
                      size="10"
                      color="#9999b3"
                      onClick={() => handleDeleteDetail(index)}
                    />
                  </View>
                ))}
            </View>
          </View>
        )}
        <Form.Item label="备注" name="notes" direction="column">
          <Textarea
            placeholder="请输入备注"
            className="py-[10px] pl-[10px] mt-[10px] w-auto h-[50px]"
            style={{ border: '1px solid #DCDCDC', borderRadius: '8px' }}
          />
        </Form.Item>
        <Button
          type="primary"
          className="mt-[10px] ml-[5px] flex items-center h-[35px] justify-center"
          onClick={handleSubmit}
        >
          提交
        </Button>
      </Form>
    </View>
  )
}

export default inject((stores: Stores) => ({
  materialApplication: stores.materialApplication,
}))(observer(Index))
