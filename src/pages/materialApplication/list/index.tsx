import { View, Button, MovableArea, MovableView, Input, Image } from '@tarojs/components'
import { useState, useMemo } from 'react'
import SearchComponents from './components/SearchComponent'
import useGetAccountList from '@/hooks/useGetAccountList'
import FlatList from '@/components/FlatList/FlatList'
import { useMount, useDebounceEffect } from 'ahooks'
import { materialOrderList, materialUpdate } from '@/api/materialApplication'
import dayjs from 'dayjs'
import Taro from '@tarojs/taro'
import { inject, observer } from 'mobx-react'
import Stores from '@/types/stores'
import { tabList, getStatusTemp, getStatusType, getStatusTypeByNumber } from '../constant'
import { screenWidth, screenHeight } from '@/utils'
import { AtIcon, AtModal, AtModalAction, AtModalContent } from 'taro-ui'
import { getPacketWarehouse } from '@/api/common'
import './index.scss'

const svgBase64 =
  'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBmaWxsPSIjNEFBQzBGIiBzdHJva2U9IiM0QUFDMEYiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIgZD0iTTEwLjI5NiA2Ljg4OUw0LjgzMyAxMS4xOGEuNS41IDAgMCAwIDAgLjc4Nmw1LjQ2MyA0LjI5MmEuNS41IDAgMCAwIC44MDEtLjQ4MmwtLjM1NS0xLjk1NWM1LjAxNi0xLjIwNCA3LjEwOCAxLjQ5NCA3LjkxNCAzLjIzNWMuMTE4LjI1NC42MTQuMjA1LjY0LS4wNzNjLjY0NS03LjIwMS00LjA4Mi04LjI0NC04LjU3LTcuNTY3bC4zNzEtMi4wNDZhLjUuNSAwIDAgMC0uOC0uNDgzIi8+PC9zdmc+'

const Index = ({ materialApplication }) => {
  const [searchValue, setSearchValue] = useState('') // 搜索框的值
  const [tabValue, setTabValue] = useState('1') // tab选中的值

  const [packetWarehouseList, setPacketWarehouseList] = useState<any[]>([])

  const [formParams, setFormParams] = useState<Partial<MaterialApplication.ListRequestForm>>({})
  const [pageIndex, setPageIndex] = useState(1)
  const [total, setTotal] = useState(0)
  //
  const [data, setData] = useState<Array<MaterialApplication.DataItem>>([])

  const [dynamicTabList, setDynamicTabList] = useState(tabList)

  const { customerData } = useGetAccountList()
  const [isOpened, setIsOpened] = useState<boolean>(false)
  const [signOffRecord, setSignOffRecord] = useState<any>()

  useMount(() => {
    fetchPacketWarehouse()
  })

  async function fetchPacketWarehouse() {
    try {
      const response = await getPacketWarehouse()
      if (response.success) {
        setPacketWarehouseList(response.data)
      }
    } catch (error) {
      console.log(error)
    }
  }

  useDebounceEffect(
    () => {
      init()
    },
    [formParams, pageIndex, tabValue, customerData, searchValue],
    { wait: 300 }
  )

  async function init() {
    try {
      const params = {
        ...formParams,
        orderId: searchValue ? searchValue : undefined,
        pageNo: pageIndex,
        pageSize: 10,
        status: getStatusTemp(tabValue),
        sourceType: '0',
        startDate: formParams?.createTimes
          ? dayjs(formParams.createTimes[0]).format('YYYY-MM-DD')
          : undefined,
        endDate: formParams?.createTimes
          ? dayjs(formParams.createTimes[1]).format('YYYY-MM-DD')
          : undefined,
      }
      const response = await materialOrderList(params)
      if (response.success) {
        if (pageIndex === 1) {
          setData(response.data?.data || [])
        } else {
          setData([...data, ...(response.data?.data || [])])
        }
        setTotal(response.data?.totalCount || 0)
        setDynamicTabList(
          tabList.map((item) => ({
            ...item,
            extra: response.data?.statusNum?.[getStatusTypeByNumber(item.value)],
          }))
        )
      }
    } catch (error) {
      console.log(error)
    }
  }

  function handleItemRender(item: MaterialApplication.DataItem, index: number) {
    function handleGoDetail(item: MaterialApplication.DataItem) {
      materialApplication.setDetailInfoParams({ ...item })
      Taro.navigateTo({
        url: '/pages/materialApplication/detail/index',
      })
    }

    const checkProperties = (obj: any) => {
      if (!obj) return 0
      const properties = ['applyNumber', 'approvalsNumber', 'apply_piece', 'signNumber']
      return properties.filter((prop) => obj[prop] !== undefined && obj[prop] !== null).length
    }

    const existingPropertiesCount = checkProperties(item?.materielList?.[0])

    function handleGoApply(item: MaterialApplication.DataItem) {
      materialApplication.setDetailInfoParams({ ...item })
      Taro.navigateTo({
        url: '/pages/materialApplication/apply/index',
      })
    }

    function handleGoSignOff(item: MaterialApplication.DataItem) {
      materialApplication.setDetailInfoParams({ ...item })
      Taro.navigateTo({
        url: '/pages/materialApplication/signOff/index',
      })
    }

    return (
      <View
        className="mt-[8px] bg-[#fff] rounded-md"
        key={index}
        onClick={() => {
          handleGoDetail(item)
        }}
      >
        <View className="p-[6px]" style={{ borderBottom: '1px solid #F6F4F4' }}>
          <View className="flex justify-between items-center p-[6px]">
            <View
              className="text-[14px] text-[#4AAC0F] flex"
              onClick={() => {
                handleGoDetail(item)
              }}
            >
              <View className="text-[#3D3D3D]">编号:</View>
              {item?.order_id}
            </View>
            {/* text-[#4AAC0F] */}
            <View className="text-[12px] text-[#9999b3]">
              {tabList?.find((val) => val?.value === getStatusType(item?.status))?.title}
            </View>
          </View>
        </View>
        <View className="p-[12px] text-[12px]">
          <View className="flex justify-between items-center ">
            <View className="font-bold">物料种类</View>
            <View>{`${
              item?.materielList?.[0]?.applyNumber !== undefined ||
              item?.materielList?.[0]?.apply_piece !== undefined
                ? '申请数'
                : ''
            }${item?.materielList?.[0]?.approvalsNumber !== undefined ? '/审批数' : ''}${
              item?.materielList?.[0]?.signNumber !== undefined ? '/签收数' : ''
            }`}</View>
          </View>
          <View className="flex justify-between items-center pt-[6px]">
            <View>{item?.materielList?.[0]?.materiel_type_name}</View>
            <View className={`flex justify-around gap-[${existingPropertiesCount * 14}px]`}>
              <View>
                {item?.materielList?.[0]?.applyNumber ?? item?.materielList?.[0]?.apply_piece ?? ''}
              </View>
              <View>{item?.materielList?.[0]?.approvalsNumber ?? ''}</View>
              <View>{item?.materielList?.[0]?.signNumber ?? ''}</View>
            </View>
          </View>
          <View className="flex justify-between items-center pt-[6px]">
            <View className="text-[#9999b3]">
              {dayjs(item?.create_time).format('YYYY-MM-DD HH:mm:ss')}
            </View>
            {getStatusType(item?.status) === '2' ? (
              <View
                className="text-[#4AAC0F]"
                onClick={(e) => {
                  e.stopPropagation()
                  handleGoApply(item)
                }}
              >
                重新申请
              </View>
            ) : getStatusType(item?.status) === '5' ? (
              <View
                className="text-[#4AAC0F]"
                onClick={(e) => {
                  e.stopPropagation()
                  handleGoSignOff(item)
                }}
              >
                签收
              </View>
            ) : getStatusType(item?.status) === '1' ? (
              <View
                className="text-[#4AAC0F] flex items-center gap-[2px]"
                onClick={(e) => {
                  e.stopPropagation()
                  setIsOpened(true)
                  setSignOffRecord(item)
                }}
              >
                <Image src={svgBase64} className="w-[20px] h-[20px]" mode="aspectFit" />
                <View>撤销</View>
              </View>
            ) : null}
          </View>
        </View>
      </View>
    )
  }

  function reset() {
    setData([])
    setTotal(0)
    setPageIndex(1)
    setSignOffRecord(undefined)
  }

  return (
    <MovableArea className="bg-content pt-[10px] w-[100vw]">
      <SearchComponents
        tabList={dynamicTabList}
        tabValue={tabValue}
        onTabChange={(value) => {
          reset()
          setTabValue(value)
        }}
        searchValue={searchValue}
        setSearchValue={(values) => {
          reset()
          setSearchValue(values)
        }}
        packetWarehouseList={packetWarehouseList}
        onSubmit={(values) => {
          reset()
          setFormParams(values)
        }}
      />
      <View className="px-[10px] py-[15px]">
        <View className="flex justify-between items-center">
          <View className="text-[12px] text-[#B9B1B1] mb-[5px]">查询到{total}条数据</View>
          {/* <Button
            type="primary"
            className="mx-0 rounded-[33px]"
            style={{ fontSize: '12px' }}
            onClick={() => {
              // problem.setDisposeInfoData({
              //   name: 'test',
              //   age: '18',
              //   sex: '男',
              // })
              Taro.navigateTo({
                url: '/pages/problem/dispose/index',
              })
            }}
          >
            批量处理
          </Button> */}
        </View>

        <FlatList
          className="h-[70vh]"
          itemData={data}
          total={total}
          itemRender={handleItemRender}
          onRefresh={async () => {
            await new Promise<any>((resolve) => {
              setTimeout(() => {
                setData([])
                if (pageIndex === 1) {
                  reset()
                  init()
                } else {
                  reset()
                }
                resolve('')
              }, 300)
            })
          }}
          onScrollToLower={async () => {
            if (data.length === total) return
            await new Promise<any>((resolve) => {
              setTimeout(() => {
                setPageIndex(pageIndex + 1)
                resolve('')
              }, 300)
            })
          }}
        />
        <AtModal isOpened={isOpened} onClose={() => setIsOpened(false)}>
          <View className="text-center rounded-[5px]">
            <AtModalContent>
              <View className="text-[17px] text-[#3D3D3D] p-[6px] leading-[22px]">撤销</View>
              {isOpened && (
                <Input
                  placeholder="请输入备注"
                  className="text-[14px] text-[#3D3D3D] p-[6px] leading-[22px]"
                  value={signOffRecord?.notes}
                  onInput={(e) => {
                    setSignOffRecord({ ...signOffRecord, notes: e.detail.value })
                  }}
                />
              )}
            </AtModalContent>
            <AtModalAction>
              <Button onClick={() => setIsOpened(false)}>取消</Button>
              <Button
                onClick={async () => {
                  if (!signOffRecord?.notes) {
                    Taro.showToast({
                      title: '请输入备注',
                      icon: 'none',
                    })
                    return
                  }
                  const response = await materialUpdate({
                    ...signOffRecord,
                    orderId: signOffRecord?.order_id,
                    operatorTime: signOffRecord?.operator_time,
                    status: 106,
                  })
                  if (response.success) {
                    Taro.showToast({
                      title: response.message,
                      icon: 'success',
                    })
                    setIsOpened(false)
                    setPageIndex(1)
                    reset()
                    init()
                  }
                }}
              >
                确定
              </Button>
            </AtModalAction>
          </View>
        </AtModal>
      </View>
      <MovableView
        style="height: 100rpx; width: 100rpx;border-radius:50%;"
        x={screenWidth - 70}
        y={screenHeight - 250}
        direction="all"
      >
        <Button
          className="w-full h-full flex justify-center items-center p-0 rounded-full bg-[#4aac11]"
          onClick={() => {
            Taro.navigateTo({
              url: '/pages/materialApplication/apply/index',
            })
          }}
        >
          <AtIcon value="add" size="12" color="#FFF" />
        </Button>
      </MovableView>
    </MovableArea>
  )
}

export default inject((stores: Stores) => ({
  materialApplication: stores.materialApplication,
}))(observer(Index))
