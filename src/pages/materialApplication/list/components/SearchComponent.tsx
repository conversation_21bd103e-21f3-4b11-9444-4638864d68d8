import { Button, Image, Input, View, ScrollView } from '@tarojs/components'
import { AtIcon } from 'taro-ui'
import '../index.scss'
import { useDebounceFn } from 'ahooks'
import PageContainerForm, { FormRefInterface } from '@/components/PageContainerForm'
import { useRef } from 'react'
import MultipleDateTimePicker, { DateFormatType } from '@/components/MultipleDateTimePicker'
import dayjs from 'dayjs'
import SearchSelectPicker from '@/components/SearchSelectPicker'
import Shaixuan from '@/static/image/icon-shaixuan.png'
import ScrollTabs from '@/components/ScrollTabs'

const startTime = dayjs().subtract(1, 'month').format('YYYY-MM-DD')
const endTime = dayjs().format('YYYY-MM-DD')

interface Props {
  tabList: Array<{
    title: string
    value: string
  }>
  packetWarehouseList: any[]
  tabValue: string
  onTabChange: (value: string) => void
  searchValue: string
  setSearchValue: (value: string) => void
  reset?: () => void
  onSubmit: (values: any) => void
}

const SearchComponents = (props: Props) => {
  const {
    tabList,
    tabValue,
    onTabChange,
    searchValue,
    setSearchValue,
    reset,
    packetWarehouseList,
    onSubmit,
  } = props
  const formRef = useRef<FormRefInterface>()
  const { run } = useDebounceFn(
    (value: string) => {
      setSearchValue(value)
    },
    { wait: 500 }
  )

  return (
    <View className="bg-[#fff] px-[11px] pt-[9px] mt-[12px]">
      <View className=" flex justify-between items-center mb-[15px]">
        <View className="flex flex-row justify-start items-center bg-[#f3f3f3] rounded-[50px] h-[30px] w-[276px]">
          <AtIcon value="search" size="7" color="#b7bac4" className="ml-[10px]" />
          <Input
            value={searchValue}
            onInput={(e) => {
              run(e.detail.value)
              if (reset) reset()
            }}
            className="ml-[9px] text-[14px] font-[400] text-[#ccc] w-[100%] text-[left]"
            placeholder="请输入工单编号"
          ></Input>
        </View>
        <View
          className="flex pr-5"
          onClick={() => {
            formRef?.current?.open()
          }}
        >
          <Image className="w-[13px] h-[13px]" src={Shaixuan} />

          <View className="ml-1 text-[14px] text-[#8D8D8D]">筛选</View>
        </View>
      </View>
      <ScrollTabs
        tabList={tabList}
        tabValue={tabValue}
        onTabChange={onTabChange}
      />

      <PageContainerForm
        formRef={formRef}
        initialValue={{
          createTimes: [startTime, endTime],
        }}
        onSubmit={async (values) => {
          try {
            onSubmit(values)
          } catch (error) {
            console.log(error)
          }
        }}
      >
        <PageContainerForm.Item label="交货仓" name="warehouseCode" type="picker">
          <SearchSelectPicker
            title="请选择交货仓"
            options={packetWarehouseList.map((val) => ({
              ...val,
              label: `${val?.name}`,
              value: val.code,
            }))}
          />
        </PageContainerForm.Item>
        <PageContainerForm.Item
          label="申请时间"
          name="createTimes"
          type="picker"
          rules={[{ required: true, message: '请选择申请时间' }]}
        >
          <MultipleDateTimePicker dateFormat={DateFormatType.YYYY_MM_DD} />
        </PageContainerForm.Item>

      </PageContainerForm>
    </View>
  )
}

export default SearchComponents
