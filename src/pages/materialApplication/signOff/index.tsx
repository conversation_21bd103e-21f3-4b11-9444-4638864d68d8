import { Button, Input, Text, View } from '@tarojs/components'
import Descriptions from '@/components/Descriptions'
import './index.scss'
import { AtIcon } from 'taro-ui'
import { inject, observer } from 'mobx-react'
import { useState } from 'react'
import { useDebounceFn, useMount, useUpdateEffect } from 'ahooks'
import { materialDetail, materialUpdate } from '@/api/materialApplication'
import Taro, { useDidHide } from '@tarojs/taro'

const Index = ({ materialApplication }) => {
  const [detailParamsInfo, setDetailParamsInfo] = useState<any>({})

  useMount(() => {
    fetchMaterialDetail()
  })

  useDidHide(() => {
    setDetailParamsInfo({})
  })

  async function fetchMaterialDetail() {
    const response = await materialDetail({
      customerCode: materialApplication.detailInfoParams.customer_code,
      orderId: materialApplication.detailInfoParams.order_id,
    })
    if (response.success) {
      setDetailParamsInfo({
        ...detailParamsInfo,
        ...response.data,
        materielList: response.data?.materielList?.map((v) => ({
          ...v,
          signNumber: v?.signNumber || '',
        })),
      })
    }
  }

  async function handleSignOff() {
    try {
      if (detailParamsInfo?.materielList?.some((v) => !v.signNumber)) {
        Taro.showToast({
          title: '请填写所有物料的签收数量',
          icon: 'none',
        })
        return
      }
      const response = await materialUpdate({
        ...detailParamsInfo,
        status: '111',
        detail: detailParamsInfo?.materielList?.map((v, i) => ({
          ...v,
          key: i,
        })),
      })

      if (response.success) {
        Taro.showToast({
          title: response.message,
          icon: 'success',
        })
        Taro.reLaunch({
          url: '/pages/materialApplication/list/index',
        })
      }
    } catch (error) {
      console.log(error)
    }
  }

  const { run } = useDebounceFn(
    (value: string, v: any, i: number) => {
      if (+value <= +v.approvalsNumber) {
        const newList = detailParamsInfo?.materielList?.map((v, index) => ({
          ...v,
          signNumber: index === i ? value : v.signNumber,
          key: index,
        }))
        setDetailParamsInfo({
          ...detailParamsInfo,
          materielList: newList,
        })
      } else {
        Taro.showToast({
          title: '签收数量不能大于审批数量',
          icon: 'none',
        })
        setDetailParamsInfo({
          ...detailParamsInfo,
          materielList: detailParamsInfo?.materielList?.map((v, index) => ({
            ...v,
            signNumber: index === i ? '' : v.signNumber,
          })),
        })
      }
    },
    { wait: 500 }
  )

  return (
    <View className="bg-content bg-[#fff]">
      <View className="p-[10px] text-[14px] flex flex-col gap-[8px]">
        <Descriptions column={1} itemClassName="sign_off_desc_border">
          <Descriptions.Item label="工单编号" childClassName="w-full text-right">
            {detailParamsInfo?.orderId}
          </Descriptions.Item>
          <Descriptions.Item label="交货仓" childClassName="w-full text-right">
            {detailParamsInfo?.warehouseName}
          </Descriptions.Item>
          <Descriptions.Item label="揽收点" childClassName="w-full text-right">
            {detailParamsInfo?.collectName}
          </Descriptions.Item>
          <Descriptions.Item label="领取方式" childClassName="w-full text-right">
            {detailParamsInfo?.sendType === 1 ? '自提' : '司机配送'}
          </Descriptions.Item>
          <Descriptions.Item label="物料使用开始日期" childClassName="w-full text-right">
            {detailParamsInfo?.useDate}
          </Descriptions.Item>
          <Descriptions.Item label="物料使用结束日期" childClassName="w-full text-right">
            {detailParamsInfo?.expireDate}
          </Descriptions.Item>
          <Descriptions.Item label="司机姓名" childClassName="w-full text-right">
            {detailParamsInfo?.driverName}
          </Descriptions.Item>
          <Descriptions.Item label="司机电话" childClassName="w-full text-right">
            {detailParamsInfo?.driverTel}
          </Descriptions.Item>
        </Descriptions>
        <View className="sign_off_desc_border">
          <View className="flex flex-row justify-between mb-[16px]">
            <View className="text-[14px] flex flex-row items-center gap-[4px] text-[#52c41a]">
              <AtIcon value="message" size="8" color="#52c41a" />
              物料使用情况
            </View>
            <View className="text-[14px]">
              <Text className="text-[#52c41a]">配送成功，待签收</Text>
            </View>
          </View>
          <View className="text-[14px] font-semibold pb-[8px]">物料种类</View>
          {detailParamsInfo?.materielList?.map((v, i) => (
            <View
              className="flex flex-col gap-[10px] py-[8px]"
              style={{ borderBottom: '1px solid #e5e5e5' }}
            >
              <View className="text-[14px] text-[#938f8f]">{v.materielTypeName}</View>
              <Descriptions column={2} itemClassName="sign_off_desc_border_item">
                <Descriptions.Item label="申请数">{v.applyNumber}</Descriptions.Item>
                <Descriptions.Item label="审批数">{v.approvalsNumber}</Descriptions.Item>
              </Descriptions>
              <Descriptions column={1} itemClassName="sign_off_desc_border_item">
                <Descriptions.Item label="签收数" labelClassName="items-center">
                  <Input
                    type="number"
                    value={v.signNumber}
                    onInput={(e) => {
                      const value = e.detail.value
                      run(value, v, i)
                      return value
                    }}
                    placeholder="请输入签收数量"
                    style={{ border: '1px solid #e5e5e5', padding: '14rpx' }}
                  />
                </Descriptions.Item>
              </Descriptions>
            </View>
          ))}
        </View>
        <View className="sign_off_desc_border">
          <View className="flex flex-row justify-between ">
            <View className="text-[14px] flex flex-row items-center gap-[4px] ">
              <AtIcon value="clock" size="8" color="#52c41a" />
              时间信息
            </View>
          </View>
          <View className="text-[14px] py-[10px]">
            <Descriptions column={1} itemClassName="sign_off_desc_border_item">
              <Descriptions.Item label="申请时间">{detailParamsInfo?.createTime}</Descriptions.Item>
            </Descriptions>
          </View>
        </View>
        <Button
          className="bg-[#52c41a] text-[#fff] w-full "
          style={{ fontSize: '16px' }}
          onClick={handleSignOff}
        >
          确认签收
        </Button>
      </View>
    </View>
  )
}

export default inject('materialApplication')(observer(Index))
