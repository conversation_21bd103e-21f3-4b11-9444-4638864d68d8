export enum WorkOrderType {
  'MATER_STATUS_LIST' = '1',
  'MATERIAL_FAILURE' = '2',
  'MATERIAL_SUCCESS' = '3',
  'MATERIAL_DELIVERING' = '4',
  'MATERIAL_VERIFYING' = '5',
  'MATERIAL_COMPLETED' = '6',
}

// Usage example:
// To get the statusTemp for a specific WorkOrderType, you can use:
// const statusTemp = statusTempMap[WorkOrderType.MATER_STATUS_LIST];
// This will give you '101,102' for MATER_STATUS_LIST.

// 定义状态显示文本映射
export const statusTextMap: Record<WorkOrderType, string> = {
  [WorkOrderType.MATER_STATUS_LIST]: '待审核',
  [WorkOrderType.MATERIAL_FAILURE]: '审核不通过',
  [WorkOrderType.MATERIAL_SUCCESS]: '审核通过',
  [WorkOrderType.MATERIAL_DELIVERING]: '配送中',
  [WorkOrderType.MATERIAL_VERIFYING]: '待签收',
  [WorkOrderType.MATERIAL_COMPLETED]: '已完成',
}

const statusTempMap: Record<WorkOrderType, string> = {
  [WorkOrderType.MATER_STATUS_LIST]: '101,102',
  [WorkOrderType.MATERIAL_FAILURE]: '104',
  [WorkOrderType.MATERIAL_SUCCESS]: '103,105,112',
  [WorkOrderType.MATERIAL_DELIVERING]: '107',
  [WorkOrderType.MATERIAL_VERIFYING]: '108',
  [WorkOrderType.MATERIAL_COMPLETED]: '109,110,111',
}

const statusTempReverseMap: Record<string, WorkOrderType> = {
  '101': WorkOrderType.MATER_STATUS_LIST,
  '102': WorkOrderType.MATER_STATUS_LIST,
  '104': WorkOrderType.MATERIAL_FAILURE,
  '103': WorkOrderType.MATERIAL_SUCCESS,
  '105': WorkOrderType.MATERIAL_SUCCESS,
  '112': WorkOrderType.MATERIAL_SUCCESS,
  '107': WorkOrderType.MATERIAL_DELIVERING,
  '108': WorkOrderType.MATERIAL_VERIFYING,
  '109': WorkOrderType.MATERIAL_COMPLETED,
  '110': WorkOrderType.MATERIAL_COMPLETED,
  '111': WorkOrderType.MATERIAL_COMPLETED,
}

export const getStatusType = (key: string | number) => {
  return statusTempReverseMap[key as keyof typeof statusTempReverseMap]
}


// interface StatusNum {
//   shsb: number // 审核不通过
//   shz: number // 待审核
//   ywc: number // 已完成
//   psz: number // 配送中
//   dqs: number // 待签收
//   shtg: number // 审核通过
// }
const statusNumberMap: Record<WorkOrderType, string> = {
  [WorkOrderType.MATERIAL_FAILURE]: 'shsb',
  [WorkOrderType.MATER_STATUS_LIST]: 'shz',
  [WorkOrderType.MATERIAL_COMPLETED]: 'ywc',
  [WorkOrderType.MATERIAL_DELIVERING]: 'psz',
  [WorkOrderType.MATERIAL_VERIFYING]: 'dqs',
  [WorkOrderType.MATERIAL_SUCCESS]: 'shtg',
}

export const getStatusTypeByNumber = (key: string) => {
  return statusNumberMap[key as keyof typeof statusNumberMap]
}

// 我需要在创建一个反推statusTempMap的map

export const getStatusTemp = (key: WorkOrderType | string) => {
  return statusTempMap[key as WorkOrderType]
}

// 生成tabList
export const tabList = Object.values(WorkOrderType).map((key) => ({
  value: key,
  title: statusTextMap[key as WorkOrderType],
}))
