import Stores from '@/types/stores'
import { ScrollView, View } from '@tarojs/components'
import { inject, observer } from 'mobx-react'
import { useMount } from 'ahooks'
import { materialDetail } from '@/api/materialApplication'
import { useState } from 'react'
import ApplyInfo from './components/ApplyInfo'
import TimeInfo from './components/TimeInfo'
import MaterialInfo from './components/MaterialInfo'
import './index.scss'

const Index = ({ materialApplication }) => {
  const [detailInfo, setDetailInfo] = useState<MaterialApplication.MaterialDetailResponse>()
  useMount(() => {
    fetchMaterialDetail()
  })

  async function fetchMaterialDetail() {
    const response = await materialDetail({
      customerCode: materialApplication.detailInfoParams.customer_code,
      orderId: materialApplication.detailInfoParams.order_id,
    })
    if (response.success) {
      setDetailInfo({ ...detailInfo, ...response.data })
    }
  }

  return (
    <View className="py-[13px] px-[12px] bg-content" catchMove>
      <ScrollView
        scrollY
        style={{ height: 'calc(100vh - 70px)' }}
      >
        <ApplyInfo detailInfo={detailInfo} />
        <TimeInfo detailInfo={detailInfo} />
        <MaterialInfo detailInfo={detailInfo} />
      </ScrollView>
    </View>
  )
}

export default inject((stores: Stores) => ({
  materialApplication: stores.materialApplication,
}))(observer(Index))
