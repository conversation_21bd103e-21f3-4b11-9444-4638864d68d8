import { View } from '@tarojs/components'
import { useState } from 'react'
import { AtAccordion } from 'taro-ui'
import { getStatusType } from '../../constant'
import Descriptions from '@/components/Descriptions'

const MaterialInfo = (props) => {
  const { detailInfo } = props

  const [open, setOpen] = useState(true)
  function handleClick(value: boolean): void {
    setOpen(value)
  }

  return (
    <View className="bg-white rounded-md text-[12px] basie_info">
      <AtAccordion
        open={open}
        hasBorder={false}
        title="物料信息"
        onClick={handleClick}
        isAnimation={false}
      >
        {detailInfo?.materielList &&
          detailInfo?.materielList.map((item) => (
            <View className="px-[12px] pb-[12px]">
              <Descriptions column={2}>
                <Descriptions.Item label="物料种类">
                  {item?.materielTypeName ?? ''}
                </Descriptions.Item>
                <Descriptions.Item label="申请数量">{item?.applyNumber ?? ''}</Descriptions.Item>
                <Descriptions.Item label="审批数量">
                  {item?.approvalsNumber ?? ''}
                </Descriptions.Item>
                <Descriptions.Item label="发放数量">{item?.sendNumber ?? ''}</Descriptions.Item>
                <Descriptions.Item label="修改原因">{item?.reducingReason ?? ''}</Descriptions.Item>
                <Descriptions.Item label="签收数量">{item?.signNumber ?? ''}</Descriptions.Item>
              </Descriptions>
            </View>
          ))}
      </AtAccordion>
    </View>
  )
}

export default MaterialInfo
