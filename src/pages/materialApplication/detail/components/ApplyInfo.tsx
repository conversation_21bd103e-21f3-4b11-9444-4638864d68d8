import Descriptions from '@/components/Descriptions'
import { View } from '@tarojs/components'
import { useState } from 'react'
import { AtAccordion } from 'taro-ui'
import { getStatusType } from '../../constant'

const ApplyInfo = (props) => {
  const { detailInfo } = props

  const [open, setOpen] = useState(true)
  function handleClick(value: boolean): void {
    setOpen(value)
  }

  return (
    <View className="bg-white rounded-md text-[12px] basie_info">
      <AtAccordion
        open={open}
        hasBorder={false}
        title="申请信息"
        onClick={handleClick}
        isAnimation={false}
      >
        <View className="px-[12px] pb-[12px]">
          <Descriptions column={1}>
            <Descriptions.Item label="工单编号">{detailInfo?.orderId}</Descriptions.Item>
          </Descriptions>
          <Descriptions column={2}>
          <Descriptions.Item label="申请来源">
              {detailInfo?.palmName === 'Portal' ? '燕文客户系统' : '万邑通'}
            </Descriptions.Item>
            <Descriptions.Item label="领取方式">
              {detailInfo?.sendType?.toString() === '0' ? '司机配送' : '自取'}
            </Descriptions.Item>
            <Descriptions.Item label="交货仓">{detailInfo?.warehouseName}</Descriptions.Item>
            {detailInfo?.status == '104' && (
              <Descriptions.Item label="审核失败原因">{detailInfo?.refuseReason}</Descriptions.Item>
            )}
            {Number(getStatusType(detailInfo?.status)) > 2 && (
              <Descriptions.Item label="揽收点">{detailInfo?.collectName}</Descriptions.Item>
            )}
            {Number(getStatusType(detailInfo?.status)) > 2 && (
              <Descriptions.Item label="司机姓名">{detailInfo?.driverName}</Descriptions.Item>
            )}
            {Number(getStatusType(detailInfo?.status)) > 2 && (
              <Descriptions.Item label="司机电话">{detailInfo?.driverTel}</Descriptions.Item>
            )}
          </Descriptions>
          <Descriptions column={1}>
            <Descriptions.Item label="备注信息">{detailInfo?.notes}</Descriptions.Item>
          </Descriptions>
        </View>
      </AtAccordion>
    </View>
  )
}

export default ApplyInfo
