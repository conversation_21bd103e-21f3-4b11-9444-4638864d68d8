import Descriptions from '@/components/Descriptions'
import { View } from '@tarojs/components'
import dayjs from 'dayjs'
import { useState } from 'react'
import { AtAccordion } from 'taro-ui'


const TimeInfo = (props) => {
  const { detailInfo } = props

  const [open, setOpen] = useState(true)
  function handleClick(value: boolean): void {
    setOpen(value)
  }

  const formatTime = (time: string) => dayjs(time).format('YYYY-MM-DD HH:mm:ss')

  return (
    <View className="bg-white rounded-md text-[12px] basie_info">
      <AtAccordion
        open={open}
        hasBorder={false}
        title="时间信息"
        onClick={handleClick}
        isAnimation={false}
      >
        <View className="px-[12px] pb-[12px]">
          <Descriptions column={1}>
            {detailInfo?.createTime && (
              <Descriptions.Item label="申请时间">
                {formatTime(detailInfo?.createTime)}
              </Descriptions.Item>
            )}
            {detailInfo?.useDate && (
              <Descriptions.Item label="物料使用日期">
                {dayjs(detailInfo?.useDate).format('YYYY-MM-DD') +
                  ' ~ ' +
                  dayjs(detailInfo?.expireDate).format('YYYY-MM-DD')}
              </Descriptions.Item>
            )}
          </Descriptions>
          <Descriptions column={2}>
            {detailInfo?.rejectTime && (
              <Descriptions.Item label="审核时间">
                {formatTime(detailInfo.rejectTime)}
              </Descriptions.Item>
            )}
            {detailInfo?.dispatchTime && (
              <Descriptions.Item label="领料时间">
                {formatTime(detailInfo.dispatchTime)}
              </Descriptions.Item>
            )}
          </Descriptions>
          <Descriptions column={2}>
            {detailInfo?.deliveryTime && (
              <Descriptions.Item label="交付时间">
                {formatTime(detailInfo.deliveryTime)}
              </Descriptions.Item>
            )}
            {detailInfo?.signTime && (
              <Descriptions.Item label="签收时间">
                {formatTime(detailInfo.signTime)}
              </Descriptions.Item>
            )}
          </Descriptions>
        </View>
      </AtAccordion>
    </View>
  )
}

export default TimeInfo
