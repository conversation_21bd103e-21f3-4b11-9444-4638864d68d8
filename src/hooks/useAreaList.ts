import { getAreaList, getCityList, getProvinceList } from '@/api/common'
import { useCallback } from 'react'

const useAreaList = () => {


  const getAreaListMethod = useCallback(async (provinceName?: string, cityName?: string) => {
    const province: Response = await getProvinceList()
    if (province.success) {
      const city: Response = await getCityList(
        provinceName ? provinceName : province.data[0].nameCn,
      )
      if (city.success) {
        const index = city.data.findIndex((item) => item.nameCn === cityName)
        const area: Response = await getAreaList(city.data[index !== -1 ? index : 0].addressCode)
        if (area.success) {
          // setAreaList()
          return [province.data, city.data, area.data]
        }
      }
    }
    return []


  }, [])


  return { getAreaListMethod }

}


export default useAreaList


