import { useInterval } from 'ahooks'
import { useState } from 'react'

export function useGetCode() {
  const [count, setCount] = useState(-1)
  const [interval, setInterval] = useState<number | undefined>(undefined)
  const [isTurn, setIsTurn] = useState(false)
  const startInterval = () => {
    if (!isTurn) {
      setCount(60)
      setInterval(1000)
      setIsTurn(true)
    } else {
      return
    }
  }
  const endInterval = () => {
    setCount(-1)
    setInterval(undefined)
    setIsTurn(false)
  }
  useInterval(
    () => {
      if (count > 0) {
        setCount(count - 1)
      } else {
        endInterval()
      }
    },
    interval,
    { immediate: false }
  )
  return {
    count,
    startInterval,
  }
}
