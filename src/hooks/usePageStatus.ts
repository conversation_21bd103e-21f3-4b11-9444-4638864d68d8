import { getPageState } from '@/api/common'
import PageStatusEnum from '@/utils/pageStatusEnum'
import Taro from '@tarojs/taro'

function usePageStatus() {
  const getPageStatus = async () => {
    const result = await getPageState()
    if (result.success) {
      if (result.data.businessProcessState == 1) {
        
        Taro.reLaunch({
          url: '/pages/home/<USER>',
          // success:(e)=>{
          //   let page=getCurrentPages().pop();
          //   page.onLoad();
          // }
        }).then(() => {})
      } else {
        Taro.redirectTo({
          url: `/pages/notLogin/index?type=${result.data.businessProcessState}`,
        }).then(() => {})
      }
      // switch (+result.data) {
      //   case PageStatusEnum.MERCHANT_INFO:
      //     Taro.redirectTo({
      //       url: '/pages/home/<USER>',
      //     })
      //     break
      //   case PageStatusEnum.AUTH_PAGE:
      //     Taro.redirectTo({
      //       url: '/pages/register/realNameMerchant/realName',
      //     })
      //     break
      //   case PageStatusEnum.MERCHANT_TIPS:
      //     break
      //   case PageStatusEnum.OFFICE_INFO:
      //     Taro.redirectTo({
      //       url: '/pages/register/registerMerchant/index?status=0',
      //     }).then(() => {})
      //     break
      //   case PageStatusEnum.CONTACT_INFO:
      //     Taro.redirectTo({
      //       url: '/pages/register/registerMerchant/index?status=1',
      //     }).then(() => {})
      //     break
      //   case PageStatusEnum.COMPANY_BANK_INFO:
      //     Taro.redirectTo({
      //       url: '/pages/register/registerMerchant/companyRegister/companyRegisterBank',
      //     })
      //     break
      //   case PageStatusEnum.COMPANY_AMOUNT_VALIDATE:
      //     Taro.redirectTo({
      //       url: '/pages/register/registerMerchant/companyRegister/companyRegisterBankVerify',
      //     })
      //     break
      //   case PageStatusEnum.SHIPPER_INFO:
      //     Taro.redirectTo({
      //       url: '/pages/register/shipperRegister/shipperRegister',
      //     }).then(() => {})
      //     break
      //   case PageStatusEnum.ERROR:
      //     Taro.redirectTo({
      //       url: '/pages/common/error',
      //     })
      //     break
      //   case PageStatusEnum.WAIT_AUDIT:
      //     Taro.redirectTo({
      //       url: '/pages/register/toAudit',
      //     })
      //     break
      //   case PageStatusEnum.AUDIT_FAIL:
      //     Taro.redirectTo({
      //       url: '/pages/register/shipperRegister/shipperRegister?status=11',
      //     }).then(() => {})
      //     break
      // }
    }
  }

  return {
    getPageStatus,
  }
}

export default usePageStatus
