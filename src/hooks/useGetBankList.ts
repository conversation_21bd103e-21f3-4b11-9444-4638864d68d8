import { useState } from 'react'
import { getBankList } from '@/api/common'

const useGetBankList = () => {
  const [bankList, setBankList] = useState<any[]>([])

  const getBankData = async () => {
    const response = await getBankList()
    if (response.success) {
      setBankList(response.data)
    }
  }

  // useEffect(() => {
  //   if (bankList.length === 0) {
  //     getBankData()
  //   }
  // }, [bankList])

  return {
    bankList,
    getBankData,
  }
}

export default useGetBankList
