import { useState } from 'react'
import { getAccountList } from '@/api/common'
import { useMount } from 'ahooks'

const useGetAccountList = () => {
  const [customerData, setCustomerData] = useState<Array<{ label: string; value: string }>>([]) // 制单账号

  const getData = async () => {
    try {
      const response = await getAccountList()
      if (response.success) {
        setCustomerData(
          response.data.map((item: any) => ({
            ...item,
            label: `${item.warehouseName}${item.accountCode}`,
            value: item.accountCode,
          }))
        )
      }
    } catch (error) {
      console.log(error)
    }
  }

  useMount(() => {
    getData()
  })

  return {
    customerData,
  }
}

export default useGetAccountList
