import { forwardRef, useImperativeHandle, useState, useEffect } from 'react'
import { View, Picker<PERSON>iew, PickerViewColumn } from '@tarojs/components'
import Taro from '@tarojs/taro'
import { AtFloatLayout, AtInput, AtButton } from 'taro-ui'
import Bank from '@/types/bank'
type Props = {
  dataList: Array<Bank> | Array<any>
  initValueIndex: number[]
  onConfirm: (value: Bank, key: string) => void
  getDataList: ((value?: any) => void) | any // 调用接口
  placeholder: string
  type: string
}
const SearchPicker = (props: Props, ref: any) => {
  const { dataList, initValueIndex, onConfirm, getDataList, placeholder, type } = props
  const [isOpen, setIsOpen] = useState(false)
  const [searchValue, setSearchValue] = useState('')
  const [selectIndex, setSelectIndex] = useState(initValueIndex)
  const [data, setData] = useState<Array<Bank>>([])
  const [makeSearchValue, setMakeSearchValue] = useState(false) // 是否需要把搜索栏的值回传

  useImperativeHandle(ref, () => {
    return {
      open: (value: boolean) => {
        setIsOpen(true)
        setMakeSearchValue(value)
      },
    }
  })

  useEffect(() => {
    if (dataList.length > 0) {
      setData(dataList)
    }
  }, [dataList])

  const handleClose = () => {
    setSelectIndex(initValueIndex)
    setSearchValue('')
    setData(dataList)
    setIsOpen(false)
  }

  const handleSearch = (value) => {
    setSearchValue(value)
    if (value) {
      if (makeSearchValue) {
        if (getDataList) {
          getDataList(value)
        } else {
          const result = dataList.filter((item) => {
            if (item.name.toString().indexOf(value.toString()) !== -1) {
              return item
            }
          })
          setData(result)
        }
      } else {
        const result = dataList.filter((item) => {
          if (item.name.toString().indexOf(value.toString()) !== -1) {
            return item
          }
        })
        setData(result)
      }
    } else {
      setData(dataList)
    }
    return value
  }

  const handleChangePicker = (e) => {
    if (e.cancelable) {
      e.preventDefault()
      e.stopPropagation()
      setSelectIndex(e.detail.value)
    }
  }

  const handleConfirm = () => {
    if (makeSearchValue) {
      const result = data[selectIndex[0]] ? data[selectIndex[0]] : { name: searchValue }
      onConfirm(result as Bank, type)
    } else {
      const result = data[selectIndex[0]]
      if (!result) return Taro.showToast({ title: '请选择银行', icon: 'none' })
      Taro.hideToast()
      onConfirm(result, type)
    }
    setSelectIndex(initValueIndex)
    setSearchValue('')
    setData(dataList)
    setIsOpen(false)
  }

  return (
    <AtFloatLayout isOpened={isOpen} onClose={handleClose}>
      <View className="mt-5 h-full">
        <View style={{ display: isOpen ? '' : 'none' }} className="px-3 z-10">
          <AtInput
            name="searchValue"
            value={searchValue}
            placeholder={placeholder}
            onChange={handleSearch}
          />
        </View>
        <PickerView
          indicatorStyle="height: 50px;"
          style="width: 100%; height: 277px;"
          value={selectIndex}
          onChange={handleChangePicker}
        >
          <PickerViewColumn>
            {data.map((item) => {
              return (
                <View className="text-center" style={{ lineHeight: '50px' }}>
                  {item.name}
                </View>
              )
            })}
          </PickerViewColumn>
        </PickerView>
        <View className="pb-2">
          <AtButton type="primary" onClick={handleConfirm}>
            确认
          </AtButton>
        </View>
      </View>
    </AtFloatLayout>
  )
}

export default forwardRef(SearchPicker)
