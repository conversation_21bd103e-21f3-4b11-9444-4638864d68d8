$themeColor: rgba (255, 90, 90, 1);
$commonBorderColor: rgba (200, 200, 200, 1);
$inputPlaceholderColor: #999999;

$commonPageBgColor: #f0f0f0;

$allowClickColor: #1dafff;

$themeColor_2: rgba (255, 90, 90, 0.2);
$theadBgColor: #20b69c;

//  可能会是通用的宽度
$commonModalWidth: 682px;

.text {
  text-align: center;
  line-height: 30px;
  font-size: 14px;
}
.datetime-layer-text {
  font-size: 14px;
  color: #666060;
}

$float-layout-height-custom-max: 400px;
$float-layout-height-custom-max: 350px;

.datetime-float-layout {
  .at-float-layout__container {
    min-height: 400px;
    .layout-body {
      min-height: 400px;
    }
    .layout-body__content {
      min-height: 400px;
    }
  }
}

.datetime-layer-select {
  padding: 10px;
  .datetime-select-button {
    padding: 5px 20px;
    border-radius: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f6f7fc;
    font-size: 14px;
    color: #666060;
  }
  .datetime-select-button-none {
    padding: 5px 40px;
    border-radius: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f6f7fc;
    font-size: 14px;
    color: #666060;
  }
  .datetime-select-button-active {
    border: 1px solid #7c97ec;
    padding: 5px 20px;
    border-radius: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #d6e4fe;
    font-size: 14px;
    color: #7c97ec;
  }
}

.datetime-layer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  .datetime-layer-title {
    display: flex;
    justify-content: space-between;
    padding: 10px;
    border-bottom: 1px solid #e5e5e5;

    .close {
      color: #666060;
      font-size: 14px;
    }
  }

  &.rotating {
    .datetime-picker {
      height: 120px !important;

      &-column {
        height: 40px !important;

        &-item {
          line-height: 40px !important;
        }
      }
    }

    .btn-group {
      margin-top: 10px !important;
      padding-top: 16px !important;

      .btn {
        height: 30px !important;
        line-height: 30px !important;
        font-size: 14px;
      }
    }
  }

  .datetime-picker-wrap {
    // background: #fff;
    width: 100%;
    // position: absolute;
    // left: 0;
    // bottom: 0;
    padding: 10px 10px 10px 10px;
    box-sizing: border-box;
    // overflow: hidden;
    // z-index: 99999;
    .datetime-picker {
      width: 100%;
      height: 150px;
      box-sizing: border-box;

      &-column {
        height: 50px;

        &-item {
          line-height: 50px;
          text-align: center;
        }
      }
      .datetime-picker-indicator {
        height: 15px;
      }
    }

    .btn-group {
      display: flex;
      flex-flow: row nowrap;
      justify-content: space-around;
      margin-bottom: 25px;
      padding-top: 5px;

      &::before {
        width: 100%;
        left: 0;
        height: 0;
      }

      .btn {
        width: 100px;
      }
    }
  }
}
