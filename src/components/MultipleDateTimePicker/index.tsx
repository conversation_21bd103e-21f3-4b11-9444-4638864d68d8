import React, { useEffect, useState } from 'react'
import { AtFloatLayout, AtIcon } from 'taro-ui'
import { View, PickerView, PickerViewColumn, But<PERSON> } from '@tarojs/components'
import './index.scss'
import Utils from '@/utils'
import { useUpdateEffect } from 'ahooks'
import dayjs from 'dayjs'
import Taro from '@tarojs/taro'
import { set } from 'mobx'

export enum DateFormatType {
  YYYY = 'YYYY',
  MM = 'MM',
  DD = 'DD',
  MMDD = 'MMDD',
  MM_DD = 'MM-DD',
  mm_dd = 'MM/DD',
  YYYYMM = 'YYYYMM',
  YYYY_MM = 'YYYY-MM',
  yyyy_mm = 'YYYY/MM',
  YYYYMMDD = 'YYYYMMDD',
  YYYY_MM_DD = 'YYYY-MM-DD',
  yyyy_mm_dd = 'YYYY/MM/DD',
  HHmm = 'HHmm', // 24小时
  hhmm = 'hhmm', // 12小时
  HHmmss = 'HHmmss',
  hhmmss = 'hhmmss',
  YYYYMMDDHHmm = 'YYYYMMDDHHmm',
  YYYYMMDDhhmm = 'YYYYMMDDhhmm',
  YYYYMMDDHHmmss = 'YYYYMMDDHHmmss',
  YYYYMMDDhhmmss = 'YYYYMMDDhhmmss',
  YYYY_MM_DD_HH_mm = 'YYYY-MM-DD HH:mm',
  YYYY_MM_DD_hh_mm = 'YYYY-MM-DD hh:mm',
  yyyy_mm_dd_HH_mm = 'YYYY/MM/DD HH:mm',
  yyyy_mm_dd_hh_mm = 'YYYY/MM/DD hh:mm',
  YYYY_MM_DD_HH_mm_ss = 'YYYY-MM-DD HH:mm:ss',
  YYYY_MM_DD_hh_mm_ss = 'YYYY-MM-DD hh:mm:ss',
  yyyy_mm_dd_HH_mm_ss = 'YYYY/MM/DD HH:mm:ss',
  yyyy_mm_dd_hh_mm_ss = 'YYYY/MM/DD hh:mm:ss',
}

interface DatetimePickerProps {
  dateFormat: DateFormatType
  onChange: (dateTime: string[]) => void
  value?: string[]
  isOpened: boolean
  isRotating?: boolean
  changeShowStatus: Function
  hideTabbar?: boolean
  disabledDate?: (current: string) => {
    disabledYear?: boolean
    disabledMonth?: boolean
    disabledDay?: boolean
  }
}

function Index(props: Partial<DatetimePickerProps>) {
  const {
    dateFormat,
    onChange: onOk,
    value: initDate,
    isRotating,
    changeShowStatus,
    isOpened,
    hideTabbar = false,
    disabledDate = undefined,
  } = props

  const [isNeedYear] = useState<boolean | undefined>(dateFormat?.includes('YYYY'))
  const [isNeedMonth] = useState<boolean | undefined>(dateFormat?.includes('MM'))
  const [isNeedDay] = useState<boolean | undefined>(dateFormat?.includes('DD'))
  const [isNeedHour] = useState<boolean | undefined>(dateFormat?.toLowerCase().includes('hh'))
  const [isNeedMinute] = useState<boolean | undefined>(dateFormat?.includes('mm'))
  const [isNeedSeconds] = useState<boolean | undefined>(dateFormat?.includes('ss'))
  const date = new Date()
  const [checkedYearIndex, setCheckedYearIndex] = useState<number>(0)
  const [checkedMonthIndex, setCheckedMonthIndex] = useState<number>(0)
  const [checkedDayIndex, setCheckedDayIndex] = useState<number>(0)
  const [checkedHourIndex, setCheckedHourIndex] = useState<number>(0)
  const [checkedMinuteIndex, setCheckedMinuteIndex] = useState<number>(0)
  const [checkedSecondsIndex, setCheckedSecondsIndex] = useState<number>(0)
  const [selectDayLoading, setSelectDayLoading] = useState<boolean>(false)
  const [selectedStartDate, setSelectedStartDate] = useState<boolean>(false)
  const [selectedEndDate, setSelectedEndDate] = useState<boolean>(false)
  const [startTime, setStartTime] = useState<string>()
  const [endTime, setEndTime] = useState<string>()
  const [dateTypeButton, setDateTypeButton] = useState<
    'today' | 'thirdDays' | 'sevenDays' | 'month' | undefined
  >()
  const [hideTodayButton, setHideTodayButton] = useState<boolean>(false)

  useUpdateEffect(() => {
    if (hideTabbar) {
      isOpened ? Taro.hideTabBar() : Taro.showTabBar()
    }
    initDatetime()
  }, [isOpened, hideTabbar])

  useUpdateEffect(() => {
    if (initDate) {
      initDatetime()
    }
  }, [initDate])

  useUpdateEffect(() => {
    getDayList()
  }, [checkedMonthIndex, checkedYearIndex])

  async function changeYearAndMonth(year, month): Promise<{ yearIndex; monthIndex }> {
    return new Promise((resolve) => {
      let yearIndex
      let monthIndex
      if (isNeedYear) {
        yearIndex = initCheckedPickerColumnIndex(
          getYearList()?.filter((val) => !disabledDate || !disabledDate(val)?.disabledYear),
          setCheckedYearIndex,
          year
        )
      }
      if (isNeedMonth) {
        monthIndex = initCheckedPickerColumnIndex(
          getMonthList()?.filter((val) => !disabledDate || !disabledDate(val)?.disabledMonth),
          setCheckedMonthIndex,
          month
        )
      }
      resolve({ yearIndex, monthIndex })
    })
  }

  function initSelectDateTime(dateTime?: string) {
    const year = dateTime?.split('-')[0] || date.getFullYear()
    const month = dateTime?.split('-')[1] || date.getMonth() + 1
    const day = dateTime?.split('-')[2] || date.getDate()
    Promise.all([changeYearAndMonth(year, month)]).then((res) => {
      const { yearIndex, monthIndex } = res[0]
      if (isNeedDay) {
        setSelectDayLoading(true)
        initCheckedPickerColumnIndex(
          getDayList(yearIndex, monthIndex)?.filter(
            (val) => !disabledDate || !disabledDate(val)?.disabledDay
          ),
          setCheckedDayIndex,
          day
        )
        setSelectDayLoading(false)
      }
    })
  }

  // 初始化选中时间
  function initDatetime(index: number = 0) {
    if (index == 0) {
      setSelectedStartDate(true)
      setSelectedEndDate(false)
    } else {
      setSelectedStartDate(false)
      setSelectedEndDate(true)
    }
    let initialDateTime = initDate?.[index]
    if (!initialDateTime && disabledDate) {
      // 获取当前日期的限制条件
      const currentDate = dayjs()
      // 根据限制条件计算初始时间
      let targetDate = currentDate
      // 如果当前日期被禁用，则向后查找第一个未被禁用的日期
      while (disabledDate(targetDate.date().toString())?.disabledDay) {
        setHideTodayButton(true)
        targetDate = targetDate.add(1, 'day')
      }
      initialDateTime = targetDate.format('YYYY-MM-DD')
      setStartTime(initialDateTime)
      setEndTime(initialDateTime)
    } else {
      setStartTime(initDate?.[0])
      setEndTime(initDate?.[1])
    }
    initSelectDateTime(initialDateTime)
  }

  /**
   * Initializes the index of the selected column
   * @param {Array} arr - The range of values
   * @param {Function} handler - The processing method
   * @param {Number | String} requirement - The value corresponding to the index
   * @returns {Number} - The index of the requirement in the array
   */
  function initCheckedPickerColumnIndex(
    arr: string[],
    handler: Function,
    requirement: number | string
  ): number {
    // Loop through the array and find the index of the requirement
    arr.forEach((val, key) => {
      // If the value of the array element is equal to the requirement
      if (Number(val) == Number(requirement)) {
        // Call the handler function with the index as the argument
        handler(key)
      }
    })
    // Return the index of the requirement in the array
    return arr.indexOf(requirement + '')
  }

  // 获取年数范围
  function getYearList(): string[] {
    let viewList: string[] = []
    for (let i = 1970; i < date.getFullYear() + 1; i++) {
      viewList.push(Utils.prefixZero(i))
    }
    return viewList
  }

  // 获取月数范围
  function getMonthList(): string[] {
    let viewList: string[] = []
    for (let i = 1; i <= 12; i++) {
      viewList.push(Utils.prefixZero(i))
    }
    return viewList
  }

  // 获取天数范围
  function getDayList(yearIndex = checkedYearIndex, monthIndex = checkedMonthIndex): string[] {
    let viewList: string[] = []
    const isLeapYear = Number(getYearList()[yearIndex]) % 4 == 0
    let dayArr = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31]
    if (isLeapYear) {
      dayArr[1] = 29
    }
    for (let i = 1; i <= dayArr[Number(getMonthList()[monthIndex]) - 1]; i++) {
      viewList.push(Utils.prefixZero(i))
    }
    return viewList
  }

  // 获取小时范围
  function getHourList(): string[] {
    let hoursView: string[] = []
    let maxHour = 13 // 12小时制
    if (dateFormat?.includes('HH')) {
      maxHour = 24
    }
    for (let i = 0; i < maxHour; i++) {
      hoursView.push(Utils.prefixZero(i))
    }
    return hoursView
  }

  // 获取分数范围
  function getMinuteList(): string[] {
    let viewList: string[] = []
    for (let i = 0; i < 60; i++) {
      viewList.push(Utils.prefixZero(i))
    }
    return viewList
  }

  // 获取秒数范围
  function getSecondList(): string[] {
    let viewList: string[] = []
    for (let i = 0; i < 60; i++) {
      viewList.push(Utils.prefixZero(i))
    }
    return viewList
  }

  useUpdateEffect(() => {
    if (selectedStartDate) {
      // 当前正在操作开始时间
      const text = `${
        getYearList()?.filter((val) => !disabledDate || !disabledDate(val)?.disabledYear)?.[
          checkedYearIndex
        ]
      }-${
        getMonthList()?.filter((val) => !disabledDate || !disabledDate(val)?.disabledMonth)?.[
          checkedMonthIndex
        ]
      }-${
        getDayList()?.filter((val) => !disabledDate || !disabledDate(val)?.disabledDay)?.[
          checkedDayIndex
        ]
      }`
      setStartTime(text)
    } else if (selectedEndDate) {
      // 当前正在操作结束时间
      const text = `${
        getYearList()?.filter((val) => !disabledDate || !disabledDate(val)?.disabledYear)?.[
          checkedYearIndex
        ]
      }-${
        getMonthList()?.filter((val) => !disabledDate || !disabledDate(val)?.disabledMonth)?.[
          checkedMonthIndex
        ]
      }-${
        getDayList()?.filter((val) => !disabledDate || !disabledDate(val)?.disabledDay)?.[
          checkedDayIndex
        ]
      }`
      setEndTime(text)
    }
  }, [
    checkedYearIndex,
    checkedMonthIndex,
    checkedDayIndex,
    checkedHourIndex,
    checkedMinuteIndex,
    checkedSecondsIndex,
  ])

  /**
   * 改变时间
   * */
  function changeDatetime(e: any) {
    const val = e.detail.value
    switch (val.length) {
      case 1:
        if (isNeedYear) {
          setCheckedYearIndex(val[0])
        }
        if (isNeedMonth) {
          setCheckedMonthIndex(val[0])
        }
        if (isNeedDay) {
          setCheckedDayIndex(val[0])
        }
        break
      case 2:
        if (dateFormat?.includes('YYYY')) {
          setCheckedYearIndex(val[0])
          setCheckedMonthIndex(val[1])
        }
        if (dateFormat?.includes('DD')) {
          setCheckedMonthIndex(val[0])
          setCheckedDayIndex(val[1])
        }
        if (dateFormat?.toLowerCase().includes('hh')) {
          setCheckedHourIndex(val[0])
          setCheckedMinuteIndex(val[1])
        }
        break
      case 3:
        if (dateFormat?.includes('YYYY')) {
          setCheckedYearIndex(val[0])
          setCheckedMonthIndex(val[1])
          setCheckedDayIndex(val[2])
        }

        if (dateFormat?.toLowerCase().includes('hh')) {
          setCheckedHourIndex(val[0])
          setCheckedMinuteIndex(val[1])
          setCheckedSecondsIndex(val[2])
        }
        break
      case 5:
      case 6:
        setCheckedYearIndex(val[0])
        setCheckedMonthIndex(val[1])
        setCheckedDayIndex(val[2])
        setCheckedHourIndex(val[3])
        if (isNeedMinute) {
          setCheckedMinuteIndex(val[4])
        }
        if (isNeedSeconds) {
          setCheckedSecondsIndex(val[5])
        }
        break
    }
  }

  // 确认时间
  function affirmDatetime() {
    if (!startTime || !endTime) {
      Taro.showToast({
        title: '请选择开始时间和结束时间',
        icon: 'none',
        duration: 2000,
      })
      return
    }
    if (dayjs(startTime).isAfter(dayjs(endTime))) {
      Taro.showToast({
        title: '开始时间不能大于结束时间',
        icon: 'none',
        duration: 2000,
      })
      return
    }
    if (onOk) onOk([startTime, endTime])
    hidePicker()
  }

  function hidePicker() {
    initDatetime()
    setSelectDayLoading(false)
    setHideTodayButton(false)
    setSelectedEndDate(false)
    setSelectedStartDate(false)
    setDateTypeButton(undefined)
    if (changeShowStatus) changeShowStatus(false)
  }

  function changeDateButton(type: 'today' | 'thirdDays' | 'sevenDays' | 'month') {
    let startTime
    let endTime
    setDateTypeButton(type)
    switch (type) {
      case 'today':
        startTime = dayjs().format('YYYY-MM-DD')
        endTime = dayjs().format('YYYY-MM-DD')
        setStartTime(startTime)
        setEndTime(endTime)
        break
      case 'thirdDays': // 三天
        if (disabledDate) {
          startTime = dayjs().format('YYYY-MM-DD')
          endTime = dayjs().add(3, 'day').format('YYYY-MM-DD')
        } else {
          startTime = dayjs().subtract(3, 'day').format('YYYY-MM-DD')
          endTime = dayjs().format('YYYY-MM-DD')
        }
        setStartTime(startTime)
        setEndTime(endTime)
        break
      case 'sevenDays':
        if (disabledDate) {
          startTime = dayjs().format('YYYY-MM-DD')
          endTime = dayjs().add(7, 'day').format('YYYY-MM-DD')
        } else {
          startTime = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
          endTime = dayjs().format('YYYY-MM-DD')
        }
        setStartTime(startTime)
        setEndTime(endTime)
        break
      case 'month':
        if (disabledDate) {
          startTime = dayjs().format('YYYY-MM-DD')
          endTime = dayjs().add(1, 'month').format('YYYY-MM-DD')
        } else {
          startTime = dayjs().subtract(1, 'month').format('YYYY-MM-DD')
          endTime = dayjs().format('YYYY-MM-DD')
        }
        setStartTime(startTime)
        setEndTime(endTime)
        break
    }
    if (selectedEndDate) {
      initSelectDateTime(endTime)
    } else {
      initSelectDateTime(startTime)
    }
  }

  return (
    <AtFloatLayout
      className="custom-float-layout"
      isOpened={isOpened ?? false}
      onClose={hidePicker}
    >
      <View className="p-[15px]">
        <View className="flex justify-between mb-[12px]">
          <View className="font-bold text-[18px]">请选择时间</View>
          <AtIcon value="close" size="9" color="#dfe2e8" onClick={hidePicker} />
        </View>
        <View className="datetime-layer-select">
          <View className="datetime-layer-text">交易时间</View>
          <View className="flex justify-between mt-2">
            {!hideTodayButton && (
              <View
                className={
                  dateTypeButton === 'today'
                    ? 'datetime-select-button-active'
                    : 'datetime-select-button'
                }
                onClick={() => {
                  changeDateButton('today')
                }}
              >
                <View>今天</View>
              </View>
            )}
            <View
              className={
                dateTypeButton === 'thirdDays'
                  ? 'datetime-select-button-active'
                  : 'datetime-select-button'
              }
              onClick={() => {
                changeDateButton('thirdDays')
              }}
            >
              <View>近三天</View>
            </View>
            <View
              className={
                dateTypeButton === 'sevenDays'
                  ? 'datetime-select-button-active'
                  : 'datetime-select-button'
              }
              onClick={() => {
                changeDateButton('sevenDays')
              }}
            >
              <View>近七天</View>
            </View>
            <View
              className={
                dateTypeButton === 'month'
                  ? 'datetime-select-button-active'
                  : 'datetime-select-button'
              }
              onClick={() => {
                changeDateButton('month')
              }}
            >
              <View>近一月</View>
            </View>
          </View>
          <View className="datetime-layer-text mt-2">自定义</View>
          <View className="flex justify-between items-center mt-2">
            <View
              className={
                selectedStartDate ? 'datetime-select-button-active' : 'datetime-select-button-none'
              }
              onClick={() => {
                initSelectDateTime(startTime)
                setSelectedStartDate(true)
                setSelectedEndDate(false)
              }}
            >
              <View>{startTime}</View>
            </View>
            <View style={{ fontSize: '10px' }}>-</View>
            <View
              className={
                selectedEndDate ? 'datetime-select-button-active' : 'datetime-select-button-none'
              }
              onClick={() => {
                initSelectDateTime(endTime)
                setSelectedEndDate(true)
                setSelectedStartDate(false)
              }}
            >
              <View>{endTime}</View>
            </View>
          </View>
        </View>
        {selectDayLoading ? (
          <>loading</>
        ) : (
          <View className="datetime-picker-wrap">
            <PickerView
              indicatorClass="datetime-picker-indicator"
              indicatorStyle="height:23rpx;"
              style="width: 100%; height: 337rpx;"
              onChange={changeDatetime}
              value={[
                checkedYearIndex,
                checkedMonthIndex,
                checkedDayIndex,
                checkedHourIndex,
                checkedMinuteIndex,
                checkedSecondsIndex,
              ]}
            >
              {isNeedYear ? (
                <PickerViewColumn>
                  {getYearList()
                    ?.filter((val) => !disabledDate || !disabledDate(val)?.disabledYear)
                    ?.map((val) => {
                      return <View className="text">{val}</View>
                    })}
                </PickerViewColumn>
              ) : null}
              {isNeedMonth ? (
                <PickerViewColumn>
                  {getMonthList()
                    ?.filter((val) => !disabledDate || !disabledDate(val)?.disabledMonth)
                    ?.map((val) => {
                      return <View className="text">{val}</View>
                    })}
                </PickerViewColumn>
              ) : null}
              {isNeedDay ? (
                <PickerViewColumn>
                  {getDayList()
                    ?.filter((val) => !disabledDate || !disabledDate(val)?.disabledDay)
                    ?.map((val) => {
                      return <View className="text">{val}</View>
                    })}
                </PickerViewColumn>
              ) : null}
              {isNeedHour ? (
                <PickerViewColumn>
                  {getHourList().map((val) => {
                    return <View className="text">{val}</View>
                  })}
                </PickerViewColumn>
              ) : null}
              {isNeedMinute ? (
                <PickerViewColumn>
                  {getMinuteList().map((val) => {
                    return <View className="text">{val}</View>
                  })}
                </PickerViewColumn>
              ) : null}
              {isNeedSeconds ? (
                <PickerViewColumn>
                  {getSecondList().map((val) => {
                    return <View className="text">{val}</View>
                  })}
                </PickerViewColumn>
              ) : null}
            </PickerView>
            <View className="flex">
              {/* <Button className="h-10 leading-10" onClick={() => hidePicker()}>
              取消
            </Button> */}
              <Button
                className="w-full h-10 leading-10 rounded-3xl my-3"
                type="primary"
                onClick={() => affirmDatetime()}
              >
                确认
              </Button>
            </View>
          </View>
        )}
      </View>
    </AtFloatLayout>
  )
}

Index.displayName = 'picker'

export default Index
