$border-color: #e8e8e8;
$label-color: #52c41a;
$item-label-color: #938f8f;

.descriptions {
  font-family: Arial, sans-serif;

  &-title {
    font-size: 16px;
    color: $label-color;
    margin-bottom: 16px;
    font-weight: bold;
  }

  &-content {
    display: flex;
    flex-direction: column;
    // gap: 8px;
  }

  &-row {
    display: flex;
    width: 100%;
    // margin-bottom: 8px;
  }

  &-item {
    display: flex;
    flex: 1;
    padding-top: 10px;
    padding-left: 10px;

    &:last-child {
      margin-right: 0;
    }

    &-content {
      display: flex;
      flex-direction: row;
    }

    &-no-label {
      display: flex;
      color: $item-label-color;
      white-space: nowrap;
    }

    &-label {
      display: flex;
      color: $item-label-color;
      white-space: nowrap;
      &::after {
        content: ':';
      }
    }

    &-value {
      padding-left: 5px;
      flex: 1;
    }

    &-text-overflow {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 117.5px;
    }
    // 添加新的多行样式类
    &-text-wrap {
      word-break: break-all;
      white-space: normal;
    }
  }
}
