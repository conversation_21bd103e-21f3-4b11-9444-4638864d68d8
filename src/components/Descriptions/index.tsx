import { View } from '@tarojs/components'
import React, { useContext, createContext, ReactNode, CSSProperties } from 'react'
import './index.scss'

// 创建上下文
const DescriptionsContext = createContext({ column: 1 })

const useDescriptionsContext = () => {
  return useContext(DescriptionsContext)
}

const Descriptions = ({ column, children, rowClassName = '', itemClassName = '' }) => {
  // 将children按列数分组
  const rows = React.Children.toArray(children).reduce<ReactNode[][]>((acc, child, index) => {
    if (index % column === 0) {
      acc.push([])
    }
    acc[acc.length - 1].push(child)
    return acc
  }, [])

  return (
    <DescriptionsContext.Provider value={{ column }}>
      <View className="descriptions-content">
        {rows.map((row, rowIndex) => (
          <View className={`descriptions-row ${rowClassName}`} key={rowIndex}>
            {row.map((col, colIndex) => (
              <View className={`descriptions-item ${itemClassName}`} key={colIndex} style={{ flex: `0 0 1` }}>
                {col}
              </View>
            ))}
          </View>
        ))}
      </View>
    </DescriptionsContext.Provider>
  )
}

Descriptions.defaultProps = {
  column: 1,
}

type DescriptionsItemProps = {
  label?: string | ReactNode
  children: ReactNode
  labelClassName?: string
  contentClassName?: string
  childClassName?: string
  childStyle?: CSSProperties
}

Descriptions.Item = (props: DescriptionsItemProps) => {
  const {
    label,
    children,
    labelClassName = '',
    contentClassName,
    childClassName,
    childStyle,
  } = props
  const { column } = useDescriptionsContext()

  return (
    <View className={`descriptions-item-content ${contentClassName}`} style={{ flex: `0 0 100%` }}>
      <View className={`descriptions-item-${label ? 'label' : 'no-label'} ${labelClassName}`}>
        {typeof label === 'string' ? `${label ?? ''}` : label}
      </View>
      <View
        className={`descriptions-item-value ${
          column > 1 ? 'descriptions-item-text-overflow' : 'descriptions-item-text-wrap'
        } ${childClassName ?? ''}`}
        style={childStyle}
      >
        {children}
      </View>
    </View>
  )
}

export default Descriptions
