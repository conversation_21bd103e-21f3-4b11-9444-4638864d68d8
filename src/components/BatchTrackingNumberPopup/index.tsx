import { Input, ScrollView, Text, View } from '@tarojs/components'
import Taro from '@tarojs/taro'
import { useState } from 'react'
import { AtButton, AtFloatLayout, AtIcon } from 'taro-ui'
import './index.scss'
import { useUpdateEffect } from 'ahooks'

type Props = {
  number?: number
  onSubmit: (value: any) => void
  required?: boolean
  placeholder?: string
}

const Index = (props: Props) => {
  const { number = 50, onSubmit, required = false, placeholder = '输入运单号/订单号查询' } = props
  const [isOpened, setIsOpened] = useState(false)
  const [inputs, setInputs] = useState([{ id: 1, value: '' }])
  const [inputValue, setInputValue] = useState('')

  useUpdateEffect(() => {
    if (isOpened && inputValue) {
      handleClear()
      handleInputs(inputValue)
    }
  }, [inputValue, isOpened])

  const handleInputs = (v) => {
    const value = v.replace(/[\s+|\t+|,]/g, ',')
    const lines = value.split(',').filter(Boolean) // 过滤掉空值

    // 获取当前所有的值用于去重
    const existingValues = inputs.map((input) => input.value.trim()).filter(Boolean)
    const uniqueLines = lines.filter((line) => !existingValues.includes(line.trim()))

    // 获取当前最后一个输入框的ID
    const lastId = inputs[inputs.length - 1].id

    // 创建新的输入项，ID从最后一个开始递增
    const newItems = uniqueLines.map((value, index) => ({
      id: lastId + index + 1,
      value: value.trim(),
    }))

    // 如果最后一个输入框是空的，就替换它，否则追加
    const currentInputs = [...inputs]
    if (currentInputs[currentInputs.length - 1].value.trim() === '') {
      currentInputs.pop()
    }

    // 确保不超过50条限制
    const combinedInputs = [...currentInputs, ...newItems]
    if (combinedInputs.length > number) {
      Taro.showToast({
        title: `不能超过${number}条`,
        icon: 'none',
      })
      // 只取前50条
      setInputs(combinedInputs.slice(0, number))
    } else {
      setInputs(combinedInputs)
    }
  }

  // 注意粘贴剪贴板的内容不能放到useEffect中 必须放到某个链接或者按钮的方法上
  const getClipboardData = () => {
    Taro.getClipboardData({
      success: (res) => {
        handleInputs(res.data)
      },
      fail: (err) => {
        console.error('获取剪贴板数据失败', err)
      },
    })
  }

  useUpdateEffect(() => {
    addInput()
  }, [inputs])
  // 每当输入框失去焦点时，就会检查最后一个输入框是否为空，如果为空则添加一个新的空白输入框。
  // 这里使用了onFocus事件来触发添加新行的逻辑，你也可以根据需要使用其他事件，比如按钮点击。
  const addInput = () => {
    if (inputs.length > number) {
      Taro.showToast({
        title: `不能超过${number}条`,
      })
    } else {
      const lastInput = inputs[inputs.length - 1]
      if (lastInput.value.trim()) {
        setInputs([...inputs, { id: inputs.length + 1, value: '' }])
      }
    }
  }
  // 输入框赋值
  const handleInputChange = (e, index) => {
    const { value } = e.target
    const newInputs = [...inputs]

    // 检查是否包含分隔符（空格、制表符或逗号）
    if (/[\s,]/.test(value)) {
      // 将所有分隔符统一替换为逗号，然后分割
      const values = value
        .replace(/[\s,]+/g, ',')
        .split(',')
        .filter(Boolean)
      // 创建新的输入项，保持 id 的连续性
      const startId = newInputs[index].id
      const newItems = values.map((val, idx) => ({
        id: startId + idx,
        value: val.trim(),
      }))
      // 替换原有输入并添加新项
      newInputs.splice(index, 1, ...newItems)
      // 重新计算后续项的 id
      for (let i = index + newItems.length; i < newInputs.length; i++) {
        newInputs[i].id = newInputs[i - 1].id + 1
      }
    } else {
      // 单个值的更新
      newInputs[index] = { ...newInputs[index], value }
    }
    setInputs(newInputs)
  }

  // 删除输入框
  const removeInput = (index) => {
    const newInputs = [...inputs]
    newInputs.splice(index, 1)
    setInputs(newInputs)
  }

  const handleClear = () => {
    setInputs([{ id: 1, value: '' }])
  }

  const handleSubmit = () => {
    // 获取数组中所有的运单号的值
    let valueArr = inputs.map((item) => item.value).filter((value) => value !== '')
    if (valueArr.length === 0 && required)
      return Taro.showToast({
        title: '请输入运单号',
        icon: 'none',
      })
    setInputValue(valueArr?.join(',') ?? '')
    onSubmit(valueArr)
    reset()
  }

  const reset = () => {
    setIsOpened(false)
    handleClear()
  }

  return (
    <View>
      <View className="flex flex-row justify-start items-center bg-[#f3f3f3] rounded-[50px] h-[30px] w-[276px]">
        <AtIcon value="search" size="7" color="#b7bac4" className="ml-[10px]" />
        <View
          //   value={''}
          //   onInput={(e) => {
          //     // run(e.detail.value)
          //     // if (reset) reset()
          //   }}
          onClick={() => setIsOpened(true)}
          className="ml-[9px] text-[14px] font-[400] text-[#ccc] w-[100%] text-[left]"
        >
          {inputValue ? inputValue : <View className="text-[#808080]">{placeholder}</View>}
        </View>
      </View>
      <AtFloatLayout className="custom-float-layout" isOpened={isOpened ?? false} onClose={reset}>
        <View className="p-[15px]">
          <View className="flex justify-between mb-[12px]">
            <View className="font-bold text-[18px]">批量查询</View>
            <AtIcon value="close" size="9" color="#dfe2e8" onClick={reset}></AtIcon>
          </View>
          <ScrollView
            scrollY
            style={{
              height: 'calc(100vh - 300px)',
            }}
          >
            {inputs.map((input, index) => (
              <View key={index} className="cnt_row w-full">
                <Text className="instruction">{index + 1} &nbsp;|</Text>
                <Input
                  type="text"
                  className="sectionInput"
                  placeholder="请输入或粘贴查询单号，一行一条"
                  value={input.value}
                  onInput={(e) => handleInputChange(e, index)}
                  onFocus={addInput}
                />
                {!(inputs.length > 1) ? (
                  ''
                ) : (
                  <AtIcon
                    value="close"
                    onClick={() => removeInput(index)}
                    size="7"
                    color="#b7bac4"
                    className="mr-[13px] ml-[10px]"
                  />
                )}
              </View>
            ))}
            <View className="row_flex">
              <Text className="grayscale-[0.55] text-[14px] opacity-60" onClick={getClipboardData}>
                粘贴运单号
              </Text>
              <View className="flex">
                <Text className="grayscale-[0.55] opacity-50 text-[14px]">可查询</Text>
                <Text className="num_green text-[14px]">{inputs.length}</Text>
                <Text className="grayscale-[0.55] opacity-50 text-[14px]">/ {number}条</Text>
              </View>
            </View>
          </ScrollView>
          <View className="row_flex">
            <AtButton
              className="wxbutton border-[0px] bg-[#DAF0CD] color4AAC0F"
              type="secondary"
              size="small"
              circle
              onClick={handleClear}
            >
              清空
            </AtButton>
            <AtButton
              className="wxbutton50"
              type="primary"
              size="small"
              onClick={handleSubmit}
              circle
            >
              查询
            </AtButton>
          </View>
        </View>
      </AtFloatLayout>
    </View>
  )
}

export default Index
