import { Image, View } from '@tarojs/components'
import Taro from '@tarojs/taro'
import { AtIcon } from 'taro-ui'

const Index = (props: {
  title: string
  icon?: string
  onClick: () => void
  children?: React.ReactNode
}) => {
  const { title, icon = 'chevron-left', onClick, children } = props
  const menuButtonBoundingClientRect = Taro.getMenuButtonBoundingClientRect()

  return (
    <View
      className="flex items-center pb-[10px] bg-[#fff]"
      style={{
        height: menuButtonBoundingClientRect.height,
        paddingTop: menuButtonBoundingClientRect.top,
      }}
    >
      <View className="flex flex-row items-center  w-full h-full text-[#535151]" onClick={onClick}>
        {children ? (
          children
        ) : (
          <View className="flex flex-row items-center gap-[3px]">
            <AtIcon value={icon} size="8" color="#535151" />
            <View className="text-[14px]">{title}</View>
          </View>
        )}
      </View>
    </View>
  )
}

export default Index
