import { PageContainer, View, ScrollView } from '@tarojs/components'
import { useState, useImperativeHandle } from 'react'
import './index.scss'
import { AtIcon } from 'taro-ui'
import { useDidHide } from '@tarojs/taro'

/**
 * Props for the PageContainerSelect component.
 */
type Props<T> = {
  needAll: boolean
  value: T
  accountData?: { label: string; value: T }[]
  nodeRef: any
  title?: string
  setValue: (value: T) => void
} & typeof View.defaultProps &
  typeof PageContainer.defaultProps & {}

/**
 * PageContainerSelect component.
 * 可用于简单的下拉选择框，支持单选，看后续需求是否需要支持多选
 */
const Index = (props: Props<string>) => {
  const { position, needAll, value, accountData, nodeRef, title, setValue } = props

  const [show, setShow] = useState(false)

  useImperativeHandle(nodeRef, () => ({
    show: () => {
      setShow(true)
    },
    hide: () => {
      setShow(false)
    },
  }))

  useDidHide(() => {
    setShow(false)
  })

  /**
   * Reset the component state.
   */
  function reset() {
    setShow(false)
  }

  /**
   * Handle the selection of an item.
   * @param value - The selected value.
   */
  function handleSelect(value: Pick<Props<string>, 'value'>['value']) {
    setValue(value)
    setShow(false)
  }

  return (
    <PageContainer show={show} position={position} onClickOverlay={reset} zIndex={1000}>
      <ScrollView className="text-center h-[50vh]" scrollY>
        <View className="p-[16px] text-[16px] text-[#3D3D3D] border-b-solid">{title}</View>
        {needAll && (
          <View
            className="flex justify-between items-center px-[15px] py-[16px] border-b-solid"
            onClick={() => handleSelect('all')}
          >
            <View style={{ color: value === 'all' ? '#07C160' : '' }}>全部</View>
            {value === 'all' && (
              <View>
                <AtIcon value="check" size={10} color={'#07C160'} />
              </View>
            )}
          </View>
        )}
        {accountData?.map((item) => (
          <View
            onClick={() => handleSelect(item?.value)}
            className="flex justify-between items-center px-[15px] py-[16px] border-b-solid"
          >
            <View style={{ color: value === item?.value ? '#07C160' : '' }}>{item?.label}</View>
            {value === item?.value && (
              <View>
                <AtIcon value="check" size={10} color={'#07C160'} />
              </View>
            )}
          </View>
        ))}
      </ScrollView>
    </PageContainer>
  )
}

Index.defaultProps = {
  position: 'top',
  needAll: true,
  value: 'all',
  title: '请选择制单账号',
}

export default Index
