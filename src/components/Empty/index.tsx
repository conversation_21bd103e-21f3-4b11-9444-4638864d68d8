import { Image, View } from '@tarojs/components'
import NoData from '@/static/image/noData.png'
import React from 'react'

interface Props {
  title?: string
  description?: string | React.ReactNode
  actions?: Array<{ text: string }>
  image?: React.ReactNode
  children?: React.ReactNode
}

const Index = (props: Props & typeof View.defaultProps) => {
  const {
    title,
    description,
    actions,
    children,
    image = <Image className="w-[180px] h-[180px]" src={NoData} />,
  } = props
  return (
    <View {...props} className={`flex flex-col items-center justify-center ${props.className}`}>
      {image && image}
      <View className="text-2xl mt-4">{title}</View>
      <View className="text-gray-500 mt-2">{description}</View>
      {actions?.map((action, index) => (
        <View key={index} className="mt-4">
          {action.text}
        </View>
      ))}
      {children}
    </View>
  )
}

export default Index
