import { View, Button, RootPortal, Text } from '@tarojs/components'
import React, { useState, useImperativeHandle, MutableRefObject, useRef } from 'react'
import { AtIcon, AtFloatLayout } from 'taro-ui'
import './index.scss'
import Taro from '@tarojs/taro'
import { useUpdateEffect } from 'ahooks'

export interface FormRefInterface {
  resetFields: () => void
  getFieldsValue: (nameList?: string[]) => any
  validateFieldsValue: (nameList?: string[]) => Promise<any>
  open: () => void
}

interface IndexProps {
  title?: string
  formRef?: MutableRefObject<FormRefInterface | any>
  children: React.ReactNode
  onSubmit?: (values: any) => Promise<any>
  initialValue?: {
    [key: string]: any
  }
  hideTabbar?: boolean
}

interface FormItemProps {
  props: {
    label: string
    name: string
    children: React.ReactNode
    type?: 'picker' | 'input'
    initialValue?: any
    childRef?: MutableRefObject<any>
    rules?: Array<{
      required?: boolean
      message?: string
      pattern?: RegExp
    }>
    multiple?: boolean
  }
}

type FormChildProps = {
  isOpened: boolean | undefined
  value: any
  changeShowStatus: () => void
  onChange: (value: any) => void
} & FormItemProps['props']

const Index = ({
  title = '筛选条件',
  formRef,
  children,
  initialValue,
  onSubmit,
  hideTabbar = false,
}: IndexProps) => {
  const [show, setShow] = useState(false)
  useImperativeHandle(formRef, () => ({
    open: () => {
      setShow(true)
    },
    resetFields: () => {
      handleReset()
    },
    getFieldsValue,
    validateFieldsValue,
  }))

  useUpdateEffect(() => {
    if (hideTabbar) {
      show ? Taro.hideTabBar() : Taro.showTabBar()
    }
  }, [hideTabbar, show])

  const enhancedChildren =
    React.Children.map(children, (child: React.ReactElement<FormItemProps['props']>) => {
      if (!child) return null
      return React.cloneElement(child, {
        key: child.props.name,
        childRef: useRef(),
        initialValue: initialValue?.[`${child.props.name}`],
      })
    }) ?? []

  function getFieldsValue(nameList?: string[]) {
    const values = {}
    enhancedChildren.forEach((child: React.ReactElement<FormChildProps>) => {
      const { childRef } = child.props
      if (!nameList) {
        values[`${child.props.name}`] = childRef?.current?.getValue()
      }
      if (nameList?.includes(child.props.name)) {
        values[`${child.props.name}`] = childRef?.current?.getValue()
      }
    })
    return values
  }

  function validateFieldsValue(nameList?: string[]) {
    return new Promise((resolve, reject) => {
      enhancedChildren.forEach((child: React.ReactElement<FormChildProps>) => {
        const { childRef, rules = [] } = child.props
        if (!nameList) {
          const value = childRef?.current?.getValue()
          if (rules?.length > 0) {
            rules.forEach((rule) => {
              const { required, message, pattern } = rule
              if (required && !value) {
                reject(message)
                Taro.showToast({
                  title: message!,
                  icon: 'none',
                })
              }
              if (pattern && !pattern.test(value)) {
                reject(message)
              }
            })
          }
        }
        if (nameList?.includes(child.props.name)) {
          const value = childRef?.current?.getValue()
          if (rules?.length > 0) {
            rules.forEach((rule) => {
              const { required, message, pattern } = rule
              if (required && !value) {
                reject(message)
              }
              if (pattern && !pattern.test(value)) {
                reject(message)
              }
            })
          }
        }
      })
      resolve(getFieldsValue(nameList))
    })
  }

  function handleReset() {
    enhancedChildren.forEach((child: React.ReactElement<FormChildProps>) => {
      const { childRef, initialValue } = child.props
      childRef?.current?.onChange(initialValue ?? undefined)
    })
  }

  async function reset() {
    setShow(false)

    handleReset()
  }

  async function handleSubmit() {
    try {
      onSubmit?.(await validateFieldsValue())
      setShow(false)
    } catch (error) {
      console.log(error)
    }
  }

  return (
    <AtFloatLayout isOpened={show} className="custom-float-layout" onClose={reset}>
      <View className="flex justify-between items-center p-[18px]">
        <View className="text-[16px] text-[#3d3d3d]">{title}</View>
        <AtIcon
          value="close"
          size={8}
          color="#CBCACA"
          onClick={() => {
            setShow(false)
          }}
        ></AtIcon>
      </View>
      <View className="py-[16px]">{enhancedChildren.map((children) => children)}</View>
      <View className="flex ">
        <Button
          style={{ lineHeight: 3 }}
          className="w-full rounded-none bg-[#FFFFFF] text-[14px] custom-button"
          onClick={() => handleReset()}
        >
          重置
        </Button>
        <Button
          style={{ lineHeight: 3 }}
          className="w-full rounded-none custom-button text-[14px]"
          type="primary"
          onClick={handleSubmit}
        >
          确认
        </Button>
      </View>
    </AtFloatLayout>
  )
}

Index.Item = (props: FormItemProps['props']) => {
  const {
    label,
    name,
    children,
    type = 'input',
    initialValue = undefined,
    childRef,
    multiple = false,
  } = props

  const [open, setOpen] = useState(false)
  const [value, setValue] = useState(initialValue)

  useImperativeHandle(childRef, () => ({
    onChange: setValue,
    getValue: () => value,
  }))

  function onChange(value: any) {
    setValue(value)
    if (onChangeEvent) onChangeEvent(value)
    return value
  }

  function onInput(e: any) {
    setValue(e.detail.value)
  }

  // const enhancedChildren = React.cloneElement(
  //   children as React.ReactElement,
  //   type === 'picker'
  //     ? {
  //         type: 'picker',
  //         isOpened: type === 'picker' ? open : undefined,
  //         // type: type === 'picker' ? 'picker' : 'input',
  //         value,
  //         changeShowStatus: () => setOpen(!open),
  //         onChange,
  //       }
  //     : {
  //         value,
  //         onInput,
  //       }
  // )

  // const {
  //   type: childType,
  //   props: { options = [], onChange: onChangeEvent },
  // } = enhancedChildren

  const {
    type: childType,
    props: { options = [], className, style, onChange: onChangeEvent },
  } = children as React.ReactElement<
    any,
    string | React.JSXElementConstructor<{ name: string; type: string }>
  >

  const childTypeName = (() => {
    if (((children as React.ReactElement)?.type as any)?.displayName) {
      return ((children as React.ReactElement)?.type as any)?.displayName
    }
    // If childType is a string, return it directly
    if (typeof childType === 'string') {
      return childType
    }
    // If childType is a component with a name property
    if (childType?.name && childType.name !== 'Index') {
      return childType.name
    }

    // Default fallback
    return ''
  })()

  const isInputType =
    childTypeName === 'input' ||
    childTypeName === 'AtInput' ||
    childTypeName === 'AtInputNumber' ||
    childTypeName === 'radio-group' ||
    childTypeName === 'textarea'

  const isPicker = childTypeName === 'picker'

  const isAtInputNumber = childTypeName === 'AtInputNumber' || childTypeName === 't'

  const setElementProps = (childTypeName: string) => {
    const propsMap = {
      picker: {
        type: 'picker',
        isOpened: childTypeName === 'picker' ? open : undefined,
        value,
        changeShowStatus: () => setOpen(!open),
        onChange,
      },
      'radio-group': {
        value,
        onChange: (e) => {
          onChange(e.detail.value)
        },
      },
      input: {
        value: isAtInputNumber ? Number(value) : value,
        onInput,
        onChange,
        style: { fontSize: '14px', ...style },
        className: isAtInputNumber ? className ?? '' : `flex-2 ${className ?? ''}`,
      },
    }
    return propsMap[childTypeName]
  }

  const enhancedChildren = React.cloneElement(children as React.ReactElement, {
    ...setElementProps(childTypeName as string),
    ...Object.fromEntries(
      Object.entries((children as React.ReactElement)?.props).filter(([key]) => key !== 'onChange')
    ),
  })

  return (
    <>
      <View
        className="py-[10px] px-[18px] flex justify-between"
        style={{ borderBottom: '1px solid #DCDCDC' }}
      >
        <View className="text-[14px] text-[#3d3d3d]">{label}</View>
        {childType !== 'input' && (
          <View
            className="text-[14px] text-[#C1BCBC] flex-1 relative"
            onClick={() => {
              setOpen(true)
            }}
          >
            <Text
              style={{
                transform: 'translateY(-50%)',
              }}
              className="absolute right-[10px] left-0 top-1/2  overflow-hidden text-ellipsis whitespace-nowrap text-right"
            >
              {!Array.isArray(value)
                ? options?.find((item) => item?.value === value)?.label ?? `请选择 >`
                : multiple
                ? value?.map((v) => options?.find((item) => item?.value === v)?.label).join(' ') ||
                  '请选择 >'
                : `${value[0]} ~ ${value[1]}`}
               
            </Text>
          </View>
        )}
        {childType === 'input' && enhancedChildren}
      </View>
      {childType !== 'input' && <RootPortal>{enhancedChildren}</RootPortal>}
    </>
  )
}

export default Index
