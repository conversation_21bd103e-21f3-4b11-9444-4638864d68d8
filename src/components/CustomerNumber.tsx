import { View } from '@tarojs/components'
import PageContainerSelect from '@/components/PageContainerSelect'
import { useRef } from 'react'

interface CustomerNumberProps {
  accountValue?: string
  accountData?: { label: string; value: string }[]
  setValue: (value: string) => void
  needAll?: boolean
}

const CustomerNumber = ({
  accountValue = undefined,
  accountData,
  setValue,
  needAll = true,
}: CustomerNumberProps) => {
  const pageContainerSelectRef = useRef<any>(null)
  return (
    <>
      <View className="bg-[#fff] flex justify-between items-center px-[11px] py-[13px]">
        <View className="text-center text-[14px] text-[#4AAC0F]">
          {accountData?.find(item => item?.value === accountValue)?.label ??
            `${needAll ? '全部' : ''}`}
        </View>
        <View
          className="text-center text-[12px] text-[#757272]"
          onClick={() => {
            pageContainerSelectRef.current.show()
          }}
        >
          切换制单账号 {'>'}
        </View>
      </View>
      <PageContainerSelect
        needAll={needAll}
        accountData={accountData}
        value={accountValue}
        nodeRef={pageContainerSelectRef}
        setValue={setValue}
      />
    </>
  )
}

export default CustomerNumber
