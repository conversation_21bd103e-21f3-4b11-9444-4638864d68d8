import { ScrollView, Text, View } from '@tarojs/components'
import { useState, useRef } from 'react'
import './index.scss'
import Empty from '../Empty'
import { useUpdateEffect } from 'ahooks'

interface IProps<T = any> {
  total?: number
  itemData: Array<T>
  itemRender: (arg0: T, index: number) => any
  onRefresh?: () => Promise<any>
  onScrollToLower?: () => Promise<any>
  lessLength?: number
  loading?: boolean
  hasMore?: boolean
}

const FlatList = <T,>(props: IProps<T> & typeof ScrollView.defaultProps) => {
  const {
    itemData,
    itemRender,
    onRefresh,
    total,
    onScrollToLower,
    lessLength = 3,
    loading = false,
  } = props
  const [refresherLoading, setRefresherLoading] = useState(false)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [hasError, setHasError] = useState(false)
  const tempScrollTop = useRef(0)

  useUpdateEffect(() => {
    if (total === 0) {
      tempScrollTop.current = 0
    }
  }, [total])

  const loadingRender = () => {
    return (
      <View className="flex justify-center items-center p-5">
        <View className="custom_at_loading">
          <View className="at-loading__ring" />
        </View>
        <Text className="ml-2">加载中...</Text>
      </View>
    )
  }

  async function handleRefresher() {
    if (isRefreshing) return
    try {
      setHasError(false)
      setIsRefreshing(true)
      setRefresherLoading(true)
      await onRefresh?.()
    } catch (error) {
      console.log(error)
      setHasError(true)
    } finally {
      setIsRefreshing(false)
      setRefresherLoading(false)
    }
  }

  async function handleScrollToLower() {
    if (isLoading || hasError) return
    try {
      setIsLoading(true)
      if (props.hasMore ?? (total && itemData?.length < total)) {
        await onScrollToLower?.()
      }
    } catch (error) {
      console.log(error)
      setHasError(true)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <ScrollView
      refresherEnabled
      enhanced={false}
      scrollY
      scrollWithAnimation
      scrollTop={tempScrollTop.current}
      refresherBackground="#f3f3f3"
      refresherTriggered={refresherLoading}
      onRefresherRefresh={handleRefresher}
      lowerThreshold={40}
      onScrollToLower={handleScrollToLower}
      pagingEnabled={false}
      onScroll={(event) => {
        tempScrollTop.current = event?.detail?.scrollTop
      }}
      {...props}
    >
      {loading ? null : itemData?.length == 0 ? (
        <Empty className="mt-10" description={
          <View className="flex flex-col items-center">
            <View className="text-[14px] text-[#999999] mb-[8px]">
              页面是空的，你可以尝试其它操作！
            </View>
            <View className="text-[18px] text-[#80C555]">暂无数据</View>
          </View>
        } />
      ) : (
        <View>{itemData?.map((item, index) => itemRender(item, index))}</View>
      )}

      {/* 错误状态显示 */}
      {hasError && (
        <View className="text-center p-5 text-red-500" onClick={async() => {
          setHasError(false)
          await onRefresh?.()
        }}>加载失败，点击重试</View>
      )}

      {/* 优化加载状态判断 */}
      {(props.hasMore ?? (total && itemData?.length < total)) && !hasError && itemData?.length > lessLength ? (
        loadingRender()
      ) : (itemData?.length > 0) ? (
        <View className="text-center p-5">没有更多了</View>
      ) : null}
    </ScrollView>
  )
}

export default FlatList
