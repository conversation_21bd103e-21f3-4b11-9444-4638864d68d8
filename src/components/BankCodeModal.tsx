import React, { useState } from 'react'
import { AtModal, AtModalContent, AtModalHeader, AtModalAction, AtInput, AtButton } from 'taro-ui'
import { View, Button, Text } from '@tarojs/components'
import Taro from '@tarojs/taro'
import { useGetCode } from '@/hooks/useGetCode'
import { createPersonalBankFlow } from '@/api/register'

type Props = {
  phone: any
  isOpened: boolean
  onClose: () => void
  onConfirm: (code: number | undefined) => void
}

function BankCodeModal(props: Props) {
  const { isOpened, onClose, onConfirm, phone } = props
  const [code, setCode] = useState(undefined)
  const { count, startInterval } = useGetCode()
  const handleChange = (value) => {
    setCode(value)
    return value
  }

  // 获取验证码
  const getMsg = async () => {
    try {
      if (count === -1) {
        const response = await createPersonalBankFlow()
        if (response.success) {
          await Taro.showToast({
            title: '验证码已发送',
          })
          getCode()
        } else {
          await Taro.showToast({
            title: response.message,
            icon: 'none',
          })
        }
      } else {
        await Taro.showToast({
          title: '倒计时结束后再发送',
        })
      }
    } catch (error) {
      console.error(error)
    }
  }

  // 倒计时
  const getCode = () => {
    startInterval()
  }

  const handleClick = async () => {
    if (code) {
      onConfirm(code)
    } else {
      await Taro.showToast({
        title: '请输入验证码',
        icon: 'none',
      })
    }
  }
  return (
    <AtModal isOpened={isOpened} onClose={onClose}>
      <AtModalHeader>
        <Text className='text-left'>请输入验证码</Text>
      </AtModalHeader>
      <AtModalContent>
        <Text className='text-sm'>验证码发送至你手机 {phone}</Text>
        <View className='flex items-center my-5'>
          <AtInput className='w-56' border type='number' maxlength={6} name='code' value={code}
                   onChange={handleChange} />
          <View className='w-30 px-1'>
            <Button onClick={getMsg} plain type='primary'>
              <Text className='text-sm'>{count === -1 ? '验证码' : count}</Text>
            </Button>
          </View>
        </View>
        <Text style={{ color: '#cdcaca' }} className='text-sm'>手机号为银行卡预留手机号</Text>
      </AtModalContent>
      <AtModalAction>
        <View className='flex justify-center flex-1'>
          <AtButton className='m-2 rounded-2xl ' type='primary' onClick={handleClick}>
            确认
          </AtButton>
        </View>
      </AtModalAction>
    </AtModal>
  )
}

export default BankCodeModal
