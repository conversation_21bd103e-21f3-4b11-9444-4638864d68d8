import { Input, View, <PERSON><PERSON><PERSON>iew, But<PERSON> } from '@tarojs/components'
import React, { useState } from 'react'
import { AtFloatLayout, AtIcon } from 'taro-ui'
import { useDebounceFn, useUpdateEffect } from 'ahooks'

type ValueType = string | string[]

interface SearchSelectPickerProps {
  onChange: (value: ValueType) => void
  value?: ValueType
  isOpened: boolean
  changeShowStatus: Function
  title: string | React.ReactNode
  options: any[]
  noSearch: boolean
  onChangeEvent?: (value: ValueType) => void
  multiple?: boolean
}

const Index = (props: Partial<SearchSelectPickerProps>) => {
  const {
    onChange,
    value,
    changeShowStatus,
    isOpened,
    title,
    options = [],
    noSearch = false,
    multiple = false,
  } = props

  const [searchValue, setSearchValue] = useState('')
  const [searchData, setSearchData] = useState(options)

  useUpdateEffect(() => {
    if (options) {
      setSearchData(options)
    }
  }, [options])

  useUpdateEffect(() => {
    // 用于处理搜索逻辑
    if (searchValue) {
      const newData = options.filter((item) =>
        item.label?.toUpperCase()?.includes(searchValue?.toUpperCase())
      )
      setSearchData(newData)
    } else {
      setSearchData(options)
    }
  }, [searchValue])

  const { run } = useDebounceFn(
    (value) => {
      setSearchValue(value)
    },
    { wait: 500 }
  )

  function reset() {
    setSearchValue('')
    setSearchData(options)
    if (changeShowStatus) changeShowStatus(false)
  }

  function handleSubmit(val: string) {
    if (!onChange) return
    if (multiple) {
      const currentValues = (value as string[]) || []
      if (val === 'all') {
        if (currentValues.includes(val)) {
          onChange([])
        } else {
          onChange([...searchData.map((item) => item.value)])
        }
        return
      }
      const newValue = currentValues.includes(val)
        ? currentValues.filter((v) => v !== val)
        : [...currentValues, val]
      onChange(newValue)
    } else {
      onChange(val)
    }
    if (!multiple) reset()
  }
  return (
    <AtFloatLayout className="custom-float-layout" isOpened={isOpened ?? false} onClose={reset}>
      <View className="p-[15px] ">
        <View className="flex justify-between mb-[12px]">
          <View className="font-bold text-[18px]">{title}</View>
          <AtIcon value="close" size="9" color="#dfe2e8" onClick={reset}></AtIcon>
        </View>
        {noSearch ? null : (
          <View className="mb-[12px] flex flex-row justify-center items-center bg-[#f7f7fb] rounded-[50px] h-[30px] ">
            <AtIcon value="search" size="7" color="#b7bac4" className="ml-[10px]" />
            <Input
              value={searchValue}
              onInput={(e) => {
                run(e.detail.value)
                //   if (reset) reset()
              }}
              className="ml-[9px] text-[14px] font-[400] text-[#ccc] w-[100%] text-[left]"
              placeholder="请输入关键词搜索"
            />
          </View>
        )}

        <ScrollView
          scrollY
          style={{
            height: 'calc(100vh - 300px)',
          }}
        >
          {searchData &&
            searchData?.map((item, index) => {
              //   if (renderItem) return renderItem(item, index) className="flex justify-between items-center px-[15px] py-[16px] border-b-solid"
              return (
                <View
                  className="flex justify-between items-center px-[15px] py-[16px] border-b-solid"
                  onClick={() => handleSubmit(item?.value)}
                >
                  {item?.label}
                  {multiple && value?.includes(item?.value) && (
                    <View>
                      <AtIcon value="check" size={10} color={'#07C160'} />
                    </View>
                  )}
                </View>
              )
            })}
          {multiple && (
            <Button type="primary" className="mt-[8px]" onClick={() => reset()}>
              确定
            </Button>
          )}
        </ScrollView>
      </View>
    </AtFloatLayout>
  )
}

Index.displayName = 'picker'

export default Index
