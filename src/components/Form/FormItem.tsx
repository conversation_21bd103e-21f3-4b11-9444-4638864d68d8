import { useImperativeHandle, useState } from 'react'
import { FormItemProps } from './index'
import React from 'react'
import { RootPortal, Text, View } from '@tarojs/components'
import { isEmpty } from 'lodash'

const FormItem: React.FC<FormItemProps['props']> = (props: FormItemProps['props']) => {
  const {
    label,
    name = undefined,
    children,
    type = 'input',
    initialValue = undefined,
    childRef,
    rules = undefined,
    noStyle = false,
    direction = 'row',
    multiple = false,
  } = props

  const [open, setOpen] = useState(false)
  const [value, setValue] = useState(initialValue)

  useImperativeHandle(childRef, () => ({
    onChange: setValue,
    getValue: () => value,
  }))

  function onChange(value: any) {
    setValue(value)
    if (onChangeEvent) onChangeEvent(value)
    return value
  }

  function onInput(e: any) {
    setValue(e.detail.value)
  }

  const {
    type: childType,
    props: { options = [], className, style, onChange: onChangeEvent },
  } = children as React.ReactElement<
    any,
    string | React.JSXElementConstructor<{ name: string; type: string }>
  >

  const childTypeName = (() => {
    if (((children as React.ReactElement)?.type as any)?.displayName) {
      return ((children as React.ReactElement)?.type as any)?.displayName
    }
    // If childType is a string, return it directly
    if (typeof childType === 'string') {
      return childType
    }
    // If childType is a component with a name property
    if (childType?.name && childType.name !== 'Index') {
      return childType.name
    }

    // Default fallback
    return ''
  })()

  const isInputType =
    childTypeName === 'input' ||
    childTypeName === 'AtInput' ||
    childTypeName === 'AtInputNumber' ||
    childTypeName === 'radio-group' ||
    childTypeName === 'textarea' ||
    childTypeName === 't'
  

  const isPicker = childTypeName === 'picker'

  const isAtInputNumber = childTypeName === 'AtInputNumber' || childTypeName === 't'


  const setElementProps = (childTypeName: string) => {
    const commonInputProps = {
      value: isAtInputNumber ? Number(value) : value,
      onInput,
      onChange,
      style: { fontSize: '14px', ...style },
      className: isAtInputNumber ? className ?? '' : `flex-2 ${className ?? ''}`,
    }

    const propsMap: Record<string, any> = {
      picker: {
        type: 'picker',
        isOpened: childTypeName === 'picker' ? open : undefined,
        value,
        changeShowStatus: () => setOpen(!open),
        onChange,
      },
      'radio-group': {
        value,
        onChange: (e) => {
          onChange(e.detail.value)
        },
      },
      input: commonInputProps,
      AtInputNumber: commonInputProps,
      t: commonInputProps,
      textarea: commonInputProps,
    }

    return propsMap[childTypeName] || {}
  }

  const enhancedChildren = React.cloneElement(children as React.ReactElement, {
    ...setElementProps(childTypeName as string),
    ...Object.fromEntries(
      Object.entries((children as React.ReactElement)?.props).filter(([key]) => key !== 'onChange' && !isAtInputNumber)
    ),
  })

  

  return (
    <View>
      <View
        className={`flex items-center ${direction === 'column' ? 'mb-[10px]' : ''}`}
        style={!noStyle ? { borderBottom: direction === 'column' ? '' : '1px solid #DCDCDC' } : {}}
      >
        <View className={`${direction === 'column' ? '' : 'py-[11px]'} pl-[5px] flex-1`}>
          <View
            className={`flex  ${direction === 'column' ? 'flex-col ' : 'flex-row items-center'}`}
          >
            <View
              className={`text-[14px] flex-1 ${
                rules &&
                rules?.findIndex((item) => item?.required) != -1 &&
                typeof label === 'string'
                  ? 'required-label'
                  : ''
              }`}
            >
              {label}
            </View>
            {isPicker && (
              <View
                className={`text-[14px] text-[${
                  isEmpty(value) ? '#C1BCBC' : '#000'
                }] flex-1 relative ${direction === 'column' ? 'py-[18px] pl-[10px] mt-[5px]' : ''}`}
                style={{
                  border: direction === 'column' ? '1px solid #DCDCDC' : '',
                  borderRadius: direction === 'column' ? '8px' : '',
                }}
                onClick={() => {
                  setOpen(true)
                }}
              >
                <Text
                  style={{
                    transform: 'translateY(-50%)',
                  }}
                  className="absolute right-[10px] left-0 top-1/2  overflow-hidden text-ellipsis whitespace-nowrap text-right"
                >
                  {!Array.isArray(value)
                    ? options?.find((item) => item?.value === value)?.label ?? '请选择 >'
                    : multiple
                    ? value
                        ?.map((v) => options?.find((item) => item?.value === v)?.label)
                        .join(' ') || '请选择 >'
                    : `${value[0]} ~ ${value[1]}`}
                </Text>
              </View>
            )}
            {isInputType && enhancedChildren}
            {/* {(isInputType || isAtInputNumber) && enhancedChildren} */}
          </View>
          {isPicker && <RootPortal>{enhancedChildren}</RootPortal>}
        </View>
      </View>
    </View>
  )
}

FormItem.displayName = 'Form.Item'

export default FormItem
