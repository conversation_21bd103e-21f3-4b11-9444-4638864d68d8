import Taro from '@tarojs/taro'
import React, { useRef, useImperativeHandle, useState, useMemo } from 'react'
import { useUpdateEffect } from 'ahooks'

export interface FormListOperation {
  add: (defaultValue?: any, insertIndex?: number) => void
  remove: (index: number | number[]) => void
  move: (from: number, to: number) => void
}

export interface FormListFieldData {
  name: number
  key: number
  /** @deprecated No need anymore Use key instead */
  fieldKey?: number
}

interface FormListProps {
  name: string | string[]
  childRef?: React.MutableRefObject<any>
  initialValue?: any[]
  children: (
    fields: FormListFieldData[],
    operation: FormListOperation,
    meta?: { errors: React.ReactNode[]; warnings: React.ReactNode[] }
  ) => React.ReactNode
}

const FormList: React.FC<FormListProps> = ({ name, childRef, children, initialValue }) => {
  const [value, setValue] = useState(initialValue ?? [])

  // 使用 useMemo 来创建 Map，避免每次渲染都创建新的 Map
  const refsMap = useMemo(() => new Map(), [])

  const getOrCreateRef = (key) => {
    if (!refsMap.has(key)) {
      refsMap.set(key, React.createRef())
    }
    return refsMap.get(key)
  }

  const flatRenderNode = (children: React.ReactNode) => {
    const flatChildren: React.ReactNode[] = []
    React.Children.forEach(children, (child: React.ReactElement<any>) => {
      if (!child) return
      if ((child.type as any)?.displayName === 'Form.Item') {
        const key = Array.isArray(child.props.name) ? child.props.name.join('.') : child.props.name
        const index = child.props.name[0]
        const name = child.props.name[1]
        const childRef = getOrCreateRef(key)
        flatChildren.push(
          React.cloneElement(child, {
            key,
            childRef: childRef,
            initialValue: initialValue
              ? initialValue?.[index]?.[`${name}`]
              : value?.[index]?.[`${name}`],
          })
        )
      } else {
        if (React.isValidElement(child) && (child as React.ReactElement<any>)?.props?.children) {
          // 递归处理子元素
          const nestedChildren = flatRenderNode((child as React.ReactElement<any>)?.props?.children)
          // 创建一个新的元素，保留原有属性，但替换children
          flatChildren.push(
            React.cloneElement(child, {
              ...(child as React.ReactElement<any>)?.props,
              children: nestedChildren,
            })
          )
        } else {
          // 如果没有子元素或不是有效的React元素，直接添加
          flatChildren.push(child)
        }
      }
    })
    return flatChildren
  }

  let childrenNode: React.ReactNode[][] | null | undefined = React.Children.map(
    children(value?.map((_, index) => ({ name: index, key: index })) ?? [], {
      add: () => {},
      remove: () => {},
      move: () => {},
    }),
    (child: React.ReactElement) => {
      if (!child) return null
      return flatRenderNode(child)
    }
  )

  const validateFormItem = (children) => {
    return new Promise((resolve, reject) => {
      let formListValues: any[] = value
      React.Children.map(children, (child: React.ReactElement) => {
        if (!child) return null
        if ((child.type as any)?.displayName === 'Form.Item') {
          const { childRef, rules = [], name } = child.props
          const formItemIndex = name[0]
          const formItemValue = childRef?.current?.getValue()
          if (rules?.length > 0) {
            rules.forEach((rule) => {
              const { required, message, pattern } = rule
              if (required && !formItemValue) {
                reject(message)
                Taro.showToast({
                  title: message!,
                  icon: 'none',
                })
              }
              if (pattern && !pattern.test(formItemValue)) {
                reject(message)
                Taro.showToast({
                  title: message!,
                  icon: 'none',
                })
              }
            })
          }
          formListValues[formItemIndex][name[1]] = formItemValue
        } else {
          if (child?.props?.children) {
            return validateFormItem(child.props.children)
          }
        }
      })
      setValue(formListValues)
      resolve(formListValues)
    })
  }

  useImperativeHandle(childRef, () => ({
    validate: async () => {
      return await validateFormItem(childrenNode)
    },
    onChange: (values) => {
      setValue(values)
    },
  }))

  return <>{childrenNode}</>
}

FormList.displayName = 'Form.List'

export default FormList
