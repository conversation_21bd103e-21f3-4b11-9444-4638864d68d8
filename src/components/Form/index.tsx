import { View } from '@tarojs/components'
import Taro from '@tarojs/taro'
import React, { MutableRefObject, useImperativeHandle, useRef, useState, useMemo } from 'react'
import './index.scss'
import { setNestedValue } from '@/utils'
import FormItem from './FormItem'
import FormList from './FormList'

export interface FormRefInterface {
  resetFields: () => void
  getFieldsValue: (nameList?: string[]) => any
  setFieldsValue: (params: { [key: string]: any }) => any
  validateFieldsValue: (nameList?: string[]) => Promise<any>
}

interface IndexProps {
  title?: string
  formRef?: MutableRefObject<FormRefInterface | any>
  children: React.ReactNode
  onSubmit?: (values: any) => Promise<any>
  initialValue?: {
    [key: string]: any
  }
}

export interface FormItemProps {
  props: {
    label: string | React.ReactNode
    name?: string | string[] | Array<string | number>
    noStyle?: boolean
    children: React.ReactNode
    type?: any
    initialValue?: any
    childRef?: MutableRefObject<any>
    rules?: Array<{
      required?: boolean
      message?: string
      pattern?: RegExp
    }>
    direction?: 'row' | 'column'
    multiple?: boolean
  }
}

type FormChildProps = {
  value: any
  changeShowStatus: () => void
  onChange: (value: any) => void
} & FormItemProps['props']

const Index = ({ title = '筛选条件', formRef, children, initialValue, onSubmit }: IndexProps) => {
  // 使用 useMemo 来创建 Map，避免每次渲染都创建新的 Map
  const refsMap = useMemo(() => new Map(), [])

  const getOrCreateRef = (key) => {
    if (!refsMap.has(key)) {
      refsMap.set(key, React.createRef())
    }
    return refsMap.get(key)
  }

  useImperativeHandle(formRef, () => ({
    resetFields: () => {
      handleReset()
    },
    getFieldsValue,
    validateFieldsValue,
    setFieldsValue,
  }))

  const renderChildren = (children) => {
    return React.Children.map(children, (child: React.ReactElement<FormItemProps['props']>) => {
      if (!child) return null
      if (
        (child.type as any)?.displayName === 'Form.Item' ||
        (child.type as any)?.displayName === 'Form.List'
      ) {
        const name = Array.isArray(child.props.name) ? child.props.name.join('.') : child.props.name
        const childRef = getOrCreateRef(name)
        return React.cloneElement(child, {
          key: name,
          childRef: childRef,
          initialValue: initialValue?.[`${name}`],
        })
      } else if (child.type === 'button') {
        return child
      } else if (child?.props?.children) {
        return React.cloneElement(child, {
          ...child.props,
          children: renderChildren(child.props.children),
        })
      }
      return child
    })
  }

  const enhancedChildren = renderChildren(children)

  function setFieldsValue(values: any) {
    if (!values) {
      return;
    }
    
    function flattenObject(obj, prefix = '') {
      return Object.keys(obj).reduce((acc, key) => {
        const pre = prefix.length ? prefix + '.' : ''
        if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
          Object.assign(acc, flattenObject(obj[key], pre + key))
        } else {
          acc[pre + key] = obj[key]
        }
        return acc
      }, {})
    }

    const flattenedValues = flattenObject(values)

    const processChild = (child: React.ReactElement<FormChildProps>) => {
      if (!React.isValidElement(child)) {
        return;
      }

      const displayName = (child.type as any)?.displayName;
      
      if (displayName === 'Form.Item' || displayName === 'Form.List') {
        const { childRef, name: fieldName } = child.props;
        const name = Array.isArray(fieldName) ? fieldName.join('.') : fieldName;
        
        if (name) {
          let value: any;
          let hasValue = false;
          
          if (name.includes('.')) {
            value = flattenedValues[name];
            hasValue = name in flattenedValues;
          } else {
            value = values[name];
            hasValue = name in values;
          }
          
          if (hasValue && childRef?.current?.onChange) {
            try {
              childRef.current.onChange(value);
            } catch (error) {
              // Keep only critical error logging
              console.error('Error setting value for:', name, error);
            }
          }
        }
      }
      
      // Process nested children
      if (child.props?.children) {
        React.Children.forEach(child.props.children, processChild);
      }
    };

    if (!enhancedChildren) {
      return;
    }
    
    React.Children.forEach(enhancedChildren, processChild);
  }

  async function getFieldsValue(nameList?: string[]) {
    const values = {}
    const setValuePromises: Array<Promise<void>> = []

    const setValue = async (values, child, childRef) => {
      let value: any
      const name = Array.isArray(child.props.name) ? child.props.name.join('.') : child.props.name
      if ((child.type as any)?.displayName === 'Form.List') {
        value = await childRef?.current?.validate()
      } else if ((child.type as any)?.displayName === 'Form.Item') {
        value = childRef?.current?.getValue()
      }
      if (name && value !== undefined) {
        if (name.includes('.')) {
          setNestedValue(values, name, value)
        } else {
          values[`${name}`] = value
        }
      }
    }

    // 修改：增加类型检查和空值判断
    const collectValues = (children: React.ReactNode) => {
      React.Children.forEach(children, (child) => {
        // 确保 child 是有效的 React 元素
        if (!React.isValidElement(child)) return

        // 检查是否是表单相关组件
        if (child.type && child.props) {
          const displayName = (child.type as any)?.displayName

          if (displayName === 'Form.List' || displayName === 'Form.Item') {
            const name = Array.isArray(child.props.name)
              ? child.props.name.join('.')
              : child.props.name

            if (!nameList || nameList.includes(name)) {
              const { childRef } = child.props
              if (childRef) {
                // 确保 childRef 存在
                setValuePromises.push(setValue(values, child, childRef))
              }
            }
          }

          // 如果有子节点，递归处理
          if (child.props.children) {
            collectValues(child.props.children)
          }
        }
      })
    }

    collectValues(enhancedChildren)
    await Promise.all(setValuePromises)
    return values
  }

  function validateFieldsValue(nameList?: string[]) {
    return new Promise(async (resolve, reject) => {
      try {
        // 收集所有表单项的验证信息
        const collectValidations = (children: React.ReactNode) => {
          const promises: Promise<void>[] = []

          React.Children.forEach(children, async (child) => {
            if (!React.isValidElement(child)) return

            // 检查是否是表单组件
            if (
              (child.type as any)?.displayName === 'Form.Item' ||
              (child.type as any)?.displayName === 'Form.List'
            ) {
              const { childRef, rules = [] } = child.props
              const name = Array.isArray(child.props.name)
                ? child.props.name.join('.')
                : child.props.name

              // 如果指定了 nameList，只验证指定的字段
              if (nameList && !nameList.includes(name)) {
                return
              }

              const validationPromise = async () => {
                let value: any

                if ((child.type as any)?.displayName === 'Form.List') {
                  value = await childRef?.current?.validate()
                } else {
                  value = childRef?.current?.getValue()
                }

                // 关键：确保这里的验证逻辑被执行
                if (!Array.isArray(value)) {
                  if (rules?.length > 0) {
                    for (const rule of rules) {
                      const { required, message, pattern } = rule
                      if (required && (value === undefined || value === null || value === '')) {
                        // 使用 throw 来确保错误被捕获
                        throw new Error(message)
                      }
                      if (pattern && !pattern.test(value)) {
                        throw new Error(message)
                      }
                    }
                  }
                }
              }

              promises.push(validationPromise())
            }

            // 递归处理子节点
            if (child.props?.children) {
              promises.push(...collectValidations(child.props.children))
            }
          })

          return promises
        }

        // 执行所有验证
        const validationPromises = collectValidations(enhancedChildren)
        try {
          await Promise.all(validationPromises)
          // 验证通过，获取值
          const values = await getFieldsValue(nameList)
          resolve(values)
        } catch (error) {
          // 显示错误提示
          Taro.showToast({
            title: error.message,
            icon: 'none',
          })
          reject(error.message)
        }
      } catch (error) {
        reject(error)
      }
    })
  }

  function handleReset() {
    // 递归处理所有表单项的重置
    const resetFormItems = (children: React.ReactNode) => {
      React.Children.forEach(children, (child) => {
        if (!React.isValidElement(child)) return

        if (child.type && child.props) {
          const displayName = (child.type as any)?.displayName

          if (displayName === 'Form.Item' || displayName === 'Form.List') {
            const { childRef, initialValue } = child.props
            // 区分 Form.List 和 Form.Item 的重置
            childRef?.current?.onChange(initialValue ?? undefined)
          }

          // 递归处理子节点
          if (child.props.children) {
            resetFormItems(child.props.children)
          }
        }
      })
    }

    resetFormItems(enhancedChildren)
  }

  function reset() {
    handleReset()
  }

  return <View>{enhancedChildren.map((children) => children)}</View>
}

Index.Item = FormItem

Index.List = FormList

export default Index
