import React from 'react'
import { View, Label } from '@tarojs/components'


type Props = {
  label: string
  children: any
}

function CustomFormItem(props: Props) {
  const { label, children } = props
  return (
    <View
      className="flex items-center"
    >
      <View className="at-input at-input::after flex-1">
        <View className="at-input__container">
          <Label className="at-input__title">{label}</Label>
          {children}
        </View>
      </View>
    </View>
  )
}

export default CustomFormItem
