.tab-select {
  color: #63a715;
  border-bottom: 1px solid #63a715;
}
.tab-select-normal {
  color: #6a6a6a;
  border-bottom: 1px solid transparent;
}

.tab-common {
  display: inline-block;
  overflow: visible;
  position: relative;
  // padding: 8px;
  padding-bottom: 15px;
  padding-top: 15px;
  margin-left: 12px;
  margin-right: 12px;

  .tab-badge {
    position: absolute;
    top: 0;
    right: 0;
    transform: translate(55%, 0%); // 向右上方偏移
    min-width: 14px; // 最小宽度，确保圆形
    height: 14px; // 固定高度
    background: #ff4d4f;
    border-radius: 50%;
    color: #fff;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
  }
}
