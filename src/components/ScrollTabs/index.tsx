import { ScrollView, View } from '@tarojs/components'
import './index.scss'
import { useState } from 'react'
import { useUpdateEffect } from 'ahooks'

interface TabItem {
  title: string
  value: string
  extra?: number
}

interface ScrollTabsProps {
  tabList: TabItem[]
  tabValue: string
  onTabChange: (value: string) => void
  className?: string
  tabExtraParams?: any
}

const ScrollTabs = ({ tabList, tabValue, onTabChange, className = '' }: ScrollTabsProps) => {
  const [tabIndex, setTabIndex] = useState(0)

  useUpdateEffect(() => {
    setTabIndex(tabList.findIndex((item) => item.value === tabValue))
  }, [tabValue])

  return (
    <ScrollView
      className={`overflow-x-scroll whitespace-nowrap flex items-center text-[14px] ${className}`}
      scrollY={false}
      scrollX={true}
      scrollIntoView={`tab-${tabIndex === 1 ? 1 : tabIndex - 1}`}
      scrollWithAnimation
      scrollIntoViewAlignment="nearest"
      scrollAnchoring
      enhanced
      showScrollbar={false}
    >
      {tabList.map((item, index) => (
        <View
          id={`tab-${index + 1}`}
          key={item.value}
          onClick={() => {
            setTabIndex(index)
            onTabChange(item.value)
          }}
          className={`tab-common ${tabValue === item.value ? 'tab-select' : 'tab-select-normal'}`}
        >
          {item.title}
          {/* 直接使用额外的元素来显示数字 */}
          {item?.extra !== undefined && item.extra > 0 && <View className="tab-badge">{item.extra}</View>}
        </View>
      ))}
    </ScrollView>
  )
}

export default ScrollTabs
