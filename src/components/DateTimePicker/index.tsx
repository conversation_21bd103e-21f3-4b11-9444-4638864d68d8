import React, { useEffect, useState } from 'react'
import { View, PickerView, PickerViewColumn, But<PERSON>, PageContainer } from '@tarojs/components'
import { AtFloatLayout } from 'taro-ui'
// import './index.scss'
import Utils from '@/utils'

export enum DateFormatType {
  YYYY = 'YYYY',
  MM = 'MM',
  DD = 'DD',
  MMDD = 'MMDD',
  MM_DD = 'MM-DD',
  mm_dd = 'MM/DD',
  YYYYMM = 'YYYYMM',
  YYYY_MM = 'YYYY-MM',
  yyyy_mm = 'YYYY/MM',
  YYYYMMDD = 'YYYYMMDD',
  YYYY_MM_DD = 'YYYY-MM-DD',
  yyyy_mm_dd = 'YYYY/MM/DD',
  HHmm = 'HHmm', // 24小时
  hhmm = 'hhmm', // 12小时
  HHmmss = 'HHmmss',
  hhmmss = 'hhmmss',
  YYYYMMDDHHmm = 'YYYYMMDDHHmm',
  YYYYMMDDhhmm = 'YYYYMMDDhhmm',
  YYYYMMDDHHmmss = 'YYYYMMDDHHmmss',
  YYYYMMDDhhmmss = 'YYYYMMDDhhmmss',
  YYYY_MM_DD_HH_mm = 'YYYY-MM-DD HH:mm',
  YYYY_MM_DD_hh_mm = 'YYYY-MM-DD hh:mm',
  yyyy_mm_dd_HH_mm = 'YYYY/MM/DD HH:mm',
  yyyy_mm_dd_hh_mm = 'YYYY/MM/DD hh:mm',
  YYYY_MM_DD_HH_mm_ss = 'YYYY-MM-DD HH:mm:ss',
  YYYY_MM_DD_hh_mm_ss = 'YYYY-MM-DD hh:mm:ss',
  yyyy_mm_dd_HH_mm_ss = 'YYYY/MM/DD HH:mm:ss',
  yyyy_mm_dd_hh_mm_ss = 'YYYY/MM/DD hh:mm:ss',
}

interface DatetimePickerProps {
  dateFormat: DateFormatType
  onOk: Function
  initDate?: string
  isOpened: boolean
  isRotating?: boolean
  changeShowStatus: Function
}

function Index(props: DatetimePickerProps) {
  const [isNeedYear] = useState<boolean>(props.dateFormat.includes('YYYY'))
  const [isNeedMonth] = useState<boolean>(props.dateFormat.includes('MM'))
  const [isNeedDay] = useState<boolean>(props.dateFormat.includes('DD'))
  const [isNeedHour] = useState<boolean>(props.dateFormat.toLowerCase().includes('hh'))
  const [isNeedMinute] = useState<boolean>(props.dateFormat.includes('mm'))
  const [isNeedSeconds] = useState<boolean>(props.dateFormat.includes('ss'))
  const date = new Date()
  const [checkedYearIndex, setCheckedYearIndex] = useState<number>(0)
  const [checkedMonthIndex, setCheckedMonthIndex] = useState<number>(0)
  const [checkedDayIndex, setCheckedDayIndex] = useState<number>(0)
  const [checkedHourIndex, setCheckedHourIndex] = useState<number>(0)
  const [checkedMinuteIndex, setCheckedMinuteIndex] = useState<number>(0)
  const [checkedSecondsIndex, setCheckedSecondsIndex] = useState<number>(0)
  const [dayList, setDayList] = useState<Array<string>>([])

  useEffect(() => {
    initDatetime()
  }, [])

  useEffect(() => {
    if (props.initDate) {
      initDatetime()
    }
  }, [props.initDate])

  useEffect(() => {
    getDayList()
  }, [checkedMonthIndex, checkedYearIndex])

  // 初始化选中时间
  function initDatetime() {
    const year = props.initDate?.split('-')[0] || date.getFullYear()
    const month = props.initDate?.split('-')[1] || date.getMonth() + 1
    const day = props.initDate?.split('-')[2] || date.getDate()
    if (isNeedYear) {
      initCheckedPickerColumnIndex(getYearList(), setCheckedYearIndex, year)
    }
    if (isNeedMonth) {
      initCheckedPickerColumnIndex(getMonthList(), setCheckedMonthIndex, month)
    }
    if (isNeedDay) {
      initCheckedPickerColumnIndex(getDayList(), setCheckedDayIndex, day)
    }
  }

  /**
   * 初始化选择列的下标
   * @param {Array} arr 取值范围
   * @param {Function} handler 处理方法
   * @param {Number} requirement 下标对应的值
   * */
  function initCheckedPickerColumnIndex(
    arr: string[],
    handler: Function,
    requirement: number | string
  ) {
    arr.forEach((val, key) => {
      if (Number(val) == Number(requirement)) {
        handler(key)
      }
    })
  }

  // 获取年数范围
  function getYearList(): string[] {
    let viewList: string[] = []
    for (let i = 1970; i < date.getFullYear() + 50; i++) {
      viewList.push(Utils.prefixZero(i))
    }
    return viewList
  }

  // 获取月数范围
  function getMonthList(): string[] {
    let viewList: string[] = []
    for (let i = 1; i <= 12; i++) {
      viewList.push(Utils.prefixZero(i))
    }
    return viewList
  }

  // 获取天数范围
  function getDayList(): string[] {
    let viewList: string[] = []
    const isLeapYear = Number(getYearList()[checkedYearIndex]) % 4 == 0
    let dayArr = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31]
    if (isLeapYear) {
      dayArr[1] = 29
    }
    for (let i = 1; i <= dayArr[Number(getMonthList()[checkedMonthIndex]) - 1]; i++) {
      viewList.push(Utils.prefixZero(i))
    }
    setDayList(viewList)
    return viewList
  }

  // 获取小时范围
  function getHourList(): string[] {
    let hoursView: string[] = []
    let maxHour = 13 // 12小时制
    if (props.dateFormat.includes('HH')) {
      maxHour = 24
    }
    for (let i = 0; i < maxHour; i++) {
      hoursView.push(Utils.prefixZero(i))
    }
    return hoursView
  }

  // 获取分数范围
  function getMinuteList(): string[] {
    let viewList: string[] = []
    for (let i = 0; i < 60; i++) {
      viewList.push(Utils.prefixZero(i))
    }
    return viewList
  }

  // 获取秒数范围
  function getSecondList(): string[] {
    let viewList: string[] = []
    for (let i = 0; i < 60; i++) {
      viewList.push(Utils.prefixZero(i))
    }
    return viewList
  }

  /**
   * 改变时间
   * */
  function changeDatetime(e: any) {
    const val = e.detail.value
    switch (val.length) {
      case 1:
        if (isNeedYear) {
          setCheckedYearIndex(val[0])
        }
        if (isNeedMonth) {
          setCheckedMonthIndex(val[0])
        }
        if (isNeedDay) {
          setCheckedDayIndex(val[0])
        }
        break
      case 2:
        if (props.dateFormat.includes('YYYY')) {
          setCheckedYearIndex(val[0])
          setCheckedMonthIndex(val[1])
        }
        if (props.dateFormat.includes('DD')) {
          setCheckedMonthIndex(val[0])
          setCheckedDayIndex(val[1])
        }
        if (props.dateFormat.toLowerCase().includes('hh')) {
          setCheckedHourIndex(val[0])
          setCheckedMinuteIndex(val[1])
        }
        break
      case 3:
        if (props.dateFormat.includes('YYYY')) {
          setCheckedYearIndex(val[0])
          setCheckedMonthIndex(val[1])
          setCheckedDayIndex(val[2])
        }

        if (props.dateFormat.toLowerCase().includes('hh')) {
          setCheckedHourIndex(val[0])
          setCheckedMinuteIndex(val[1])
          setCheckedSecondsIndex(val[2])
        }
        break
      case 5:
      case 6:
        setCheckedYearIndex(val[0])
        setCheckedMonthIndex(val[1])
        setCheckedDayIndex(val[2])
        setCheckedHourIndex(val[3])
        if (isNeedMinute) {
          setCheckedMinuteIndex(val[4])
        }
        if (isNeedSeconds) {
          setCheckedSecondsIndex(val[5])
        }
        break
    }
  }

  // 确认时间
  function affirmDatetime() {
    const year = getYearList()[checkedYearIndex],
      month = getMonthList()[checkedMonthIndex],
      day = getDayList()[checkedDayIndex],
      hour = getHourList()[checkedHourIndex],
      minute = getMinuteList()[checkedMinuteIndex],
      seconds = getSecondList()[checkedSecondsIndex]

    let dateTime = ''
    let dateArr: string[] = []
    let timeArr: string[] = []
    if (isNeedYear) {
      dateArr.push(year)
    }
    if (isNeedMonth) {
      dateArr.push(month)
    }
    if (isNeedDay) {
      dateArr.push(day)
    }
    if (isNeedHour) {
      timeArr.push(hour)
    }
    if (isNeedMinute) {
      timeArr.push(minute)
    }
    if (isNeedSeconds) {
      timeArr.push(seconds)
    }
    if (props.dateFormat.includes('-')) {
      dateTime = dateArr.join('-')
    }
    if (props.dateFormat.includes('/')) {
      dateTime = dateArr.join('/')
    }
    if (dateTime == null || dateTime == '') {
      dateTime = dateArr.join('')
    }
    if (props.dateFormat.includes(' ')) {
      dateTime += ' '
    }
    if (props.dateFormat.includes(':')) {
      dateTime += timeArr.join(':')
    } else {
      dateTime += timeArr.join('')
    }
    props.onOk(dateTime)
    hidePicker()
  }

  function hidePicker() {
    props.changeShowStatus(false)
  }

  return (
    <AtFloatLayout isOpened={props.isOpened} onClose={hidePicker}>
      <View className={`datetime-layer ${props.isRotating ? 'rotating' : ''}`}>
        <View className="datetime-picker-wrap">
          <PickerView
            indicatorStyle="height: 50px;"
            style="width: 100%; height: 277px;"
            onChange={changeDatetime}
            value={[
              checkedYearIndex,
              checkedMonthIndex,
              checkedDayIndex,
              checkedHourIndex,
              checkedMinuteIndex,
              checkedSecondsIndex,
            ]}
          >
            {isNeedYear ? (
              <PickerViewColumn>
                {getYearList().map(val => {
                  return <View style={{ textAlign: 'center', lineHeight: '50px' }}>{val}</View>
                })}
              </PickerViewColumn>
            ) : null}
            {isNeedMonth ? (
              <PickerViewColumn>
                {getMonthList().map(val => {
                  return <View style={{ textAlign: 'center', lineHeight: '50px' }}>{val}</View>
                })}
              </PickerViewColumn>
            ) : null}
            {isNeedDay ? (
              <PickerViewColumn>
                {dayList.map(val => {
                  return <View style={{ textAlign: 'center', lineHeight: '50px' }}>{val}</View>
                })}
              </PickerViewColumn>
            ) : null}
            {isNeedHour ? (
              <PickerViewColumn>
                {getHourList().map(val => {
                  return <View style={{ textAlign: 'center', lineHeight: '50px' }}>{val}</View>
                })}
              </PickerViewColumn>
            ) : null}
            {isNeedMinute ? (
              <PickerViewColumn>
                {getMinuteList().map(val => {
                  return <View style={{ textAlign: 'center', lineHeight: '50px' }}>{val}</View>
                })}
              </PickerViewColumn>
            ) : null}
            {isNeedSeconds ? (
              <PickerViewColumn>
                {getSecondList().map(val => {
                  return <View style={{ textAlign: 'center', lineHeight: '50px' }}>{val}</View>
                })}
              </PickerViewColumn>
            ) : null}
          </PickerView>
          <View className="flex justify-around mb-3">
            <Button className="h-10 leading-10" onClick={() => hidePicker()}>
              取消
            </Button>
            <Button className="h-10 leading-10" type="primary" onClick={() => affirmDatetime()}>
              确认
            </Button>
          </View>
        </View>
      </View>
    </AtFloatLayout>
  )
}

export default Index
