$themeColor: rgba (
  255,
  90,
  90,
  1
);
$commonBorderColor: rgba (
  200,
  200,
  200,
  1
);
$inputPlaceholderColor: #999999;

$commonPageBgColor: #F0F0F0;

$allowClickColor: #1DAFFF;

$themeColor_2: rgba (
  255,
  90,
  90,
  0.2
);
$theadBgColor: #20B69C;

//  可能会是通用的宽度
$commonModalWidth: 682px;

.datetime-layer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;

  &.rotating {
    .datetime-picker {
      height: 120px !important;

      &-column {
        height: 40px !important;

        &-item {
          line-height: 40px !important;
        }
      }
    }

    .btn-group {
      margin-top: 10px !important;
      padding-top: 16px !important;

      .btn {
        height: 30px !important;
        line-height: 30px !important;
        font-size: 14px;
      }

    }
  }

  .datetime-picker-wrap {
    background: #fff;
    width: 100%;
    position: absolute;
    left: 0;
    bottom: 0;
    padding: 20px 20px 20px 20px;
    box-sizing: border-box;
    overflow: hidden;
    z-index: 1000;
    .datetime-picker {
      width: 100%;
      height: 300px;
      box-sizing: border-box;

      &-column {
        height: 100px;

        &-item {
          line-height: 100px;
          text-align: center;
        }
      }
    }

    .btn-group {
      display: flex;
      flex-flow: row nowrap;
      justify-content: space-around;
      margin-bottom: 50rpx;
      padding-top: 10px;

      &::before {
        width: 100%;
        left: 0;
        height: 0;
      }

      .btn {
        width: 200px;


      }


    }


  }
}
