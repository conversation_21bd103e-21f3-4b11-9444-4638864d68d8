import { PageContainer, View, ScrollView } from '@tarojs/components'
import { useState, useImperativeHandle } from 'react'
import './index.scss'
import { AtIcon } from 'taro-ui'
import { useDidHide } from '@tarojs/taro'

/**
 * Props for the PageContainerSelect component.
 */
type Props<T> = {
  value: T
  statusList?: { label: string; value: T }[]
  nodeRef: any
  title?: string
  setValue: (value: T) => void
} & typeof View.defaultProps &
  typeof PageContainer.defaultProps & {}

/**
 * PageContainerSelect component.
 * 可用于简单的下拉选择框，支持单选，看后续需求是否需要支持多选
 */
const Index = (props: Props<string>) => {
  const { position, value, statusList, nodeRef, title, setValue } = props

  const [show, setShow] = useState(false)

  useImperativeHandle(nodeRef, () => ({
    show: () => {
      setShow(true)
    },
    hide: () => {
      setShow(false)
    },
  }))

  useDidHide(() => {
    setShow(false)
  })

  /**
   * Reset the component state.
   */
  function reset() {
    setShow(false)
  }

  /**
   * Handle the selection of an item.
   * @param value - The selected value.
   */
  function handleSelect(value: Pick<Props<string>, 'value'>['value']) {
    setValue(value)
    setShow(false)
  }

  return (
    <PageContainer show={show} position={position} onClickOverlay={reset}>
      <ScrollView className="text-center h-[50vh]" scrollY>
        <View className="p-[16px] text-[16px] text-[#3D3D3D] border-b-solid">{title}</View>
        {statusList?.map(item => (
          <View
            onClick={() => handleSelect(item?.value)}
            className="flex justify-between items-center px-[15px] py-[16px] border-b-solid"
          >
            <View style={{ color: value === item?.value ? '#07C160' : '' }}>{item?.label}</View>
            {value === item?.value && (
              <View>
                <AtIcon value="check" size={10} color={'#07C160'} />
              </View>
            )}
          </View>
        ))}
      </ScrollView>
    </PageContainer>
  )
}

Index.defaultProps = {
  position: 'top',
  needAll: true,
  value: 'all',
  title: '请选择查询状态',
}

export default Index
